#!/usr/bin/env python3
"""
Générateur automatique de Jupyter Notebooks pour tous les modèles Django
Crée un notebook par modèle pour l'export CSV et l'affichage DataFrame
"""

import os
import json
from datetime import datetime

# Configuration des modèles avec leurs champs principaux
MODELS_CONFIG = {
    "UserProfile": {
        "description": "Profils utilisateur avec informations personnelles",
        "fields": ["user", "profile_picture", "created_at", "updated_at"],
        "category": "Utilisateurs"
    },
    "DQEData": {
        "description": "Données DQE (Devis Quantitatif Estimatif)",
        "fields": ["title", "description", "data", "created_at", "updated_at"],
        "category": "DQE"
    },
    "GStockApprovisionnement": {
        "description": "Données d'approvisionnement de stock",
        "fields": ["title", "description", "data", "created_at", "updated_at"],
        "category": "G-Stock"
    },
    "GStockSortie": {
        "description": "Données de sortie de stock",
        "fields": ["title", "description", "data", "created_at", "updated_at"],
        "category": "G-Stock"
    },
    "GStockConsommation": {
        "description": "Données de consommation de stock",
        "fields": ["title", "description", "data", "created_at", "updated_at"],
        "category": "G-Stock"
    },
    "GStockAchat": {
        "description": "Données d'achat de matériaux",
        "fields": ["title", "description", "data", "created_at", "updated_at"],
        "category": "G-Stock"
    },
    "GProjet": {
        "description": "Données des projets de construction",
        "fields": ["title", "description", "data", "created_at", "updated_at"],
        "category": "Projets"
    },
    "EcoleTalents": {
        "description": "Données de l'école des talents",
        "fields": ["title", "description", "data", "created_at", "updated_at"],
        "category": "Formation"
    },
    "DonneesProduitInterieurBrut": {
        "description": "Données du Produit Intérieur Brut (PIB)",
        "fields": ["annee_deb_couv", "mois_deb_couv", "annee_fin_couv", "mois_fin_couv", "valeur", "created_at", "updated_at"],
        "category": "Macroéconomie"
    },
    "donneesInflation": {
        "description": "Données d'inflation par pays",
        "fields": ["country_name", "country_code", "indicator_name", "indicator_code", "year", "value", "created_at", "updated_at"],
        "category": "Macroéconomie"
    },
    "DonnesTauxPretImmobilier": {
        "description": "Taux de prêt immobilier par banque",
        "fields": ["banque", "type_pret", "taux_interet", "duree_pret", "frais_dossier", "conditions", "source", "created_at", "updated_at"],
        "category": "Immobilier"
    },
    "DonneesPrixMetreCarre": {
        "description": "Prix au mètre carré par commune",
        "fields": ["commune", "prix_min", "prix_max", "prix_moyen", "source", "created_at", "updated_at"],
        "category": "Immobilier"
    },
    "DonneesMateriauxConstruction": {
        "description": "Prix des matériaux de construction",
        "fields": ["categorie", "titre", "prix", "description", "created_at", "updated_at"],
        "category": "Immobilier"
    },
    "DonneesProjectionDemographique": {
        "description": "Projections démographiques par région",
        "fields": ["region", "annee", "population_totale", "population_urbaine", "population_rurale", "taux_croissance", "densite", "source", "created_at", "updated_at"],
        "category": "Démographie"
    },
    "DonneesMigrationInterne": {
        "description": "Données de migration interne entre régions",
        "fields": ["annee_reference", "region_origine", "region_destination", "nombre_migrants_total", "source_donnee", "created_at", "updated_at"],
        "category": "Démographie"
    }
}

def create_notebook_content(model_name, model_config):
    """Crée le contenu JSON d'un notebook Jupyter pour un modèle donné"""

    notebook_content = {
        "cells": [
            # Cellule de titre
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    f"# Analyse des données - {model_name}\n",
                    "\n",
                    f"**Description**: {model_config['description']}\n",
                    f"**Catégorie**: {model_config['category']}\n",
                    f"**Date de création**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n",
                    "\n",
                    "## Objectifs\n",
                    "1. Charger les données du modèle depuis la base de données\n",
                    "2. Afficher les données sous forme de DataFrame pandas\n",
                    "3. Exporter les données en format CSV\n",
                    "4. Effectuer une analyse exploratoire des données\n",
                    "5. Générer des visualisations\n"
                ]
            },
            
            # Cellule d'imports
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Imports nécessaires\n",
                    "import os\n",
                    "import sys\n",
                    "import django\n",
                    "import pandas as pd\n",
                    "import numpy as np\n",
                    "import matplotlib.pyplot as plt\n",
                    "import seaborn as sns\n",
                    "from datetime import datetime\n",
                    "import warnings\n",
                    "warnings.filterwarnings('ignore')\n",
                    "\n",
                    "# Configuration de l'affichage\n",
                    "pd.set_option('display.max_columns', None)\n",
                    "pd.set_option('display.max_rows', 100)\n",
                    "plt.style.use('seaborn-v0_8')\n",
                    "sns.set_palette('husl')\n",
                    "\n",
                    "print(\"Imports terminés avec succès\")"
                ]
            },
            
            # Cellule de configuration Django
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Configuration Django\n",
                    "sys.path.append('../backend')\n",
                    "os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kay_analytics.settings')\n",
                    "django.setup()\n",
                    "\n",
                    "# Import du modèle\n",
                    f"from api.models import {model_name}\n",
                    "\n",
                    "print(\"Configuration Django terminée\")\n",
                    f"print(f\"Modèle {model_name} importé avec succès\")"
                ]
            },
            
            # Cellule de chargement des données
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Chargement des données\n",
                    f"print(\"Chargement des données du modèle {model_name}...\")\n",
                    "\n",
                    f"# Récupération de toutes les données\n",
                    f"queryset = {model_name}.objects.all()\n",
                    f"total_count = queryset.count()\n",
                    "\n",
                    f"print(f\"Nombre total d'enregistrements: {{total_count}}\")\n",
                    "\n",
                    f"if total_count == 0:\n",
                    f"    print(\"Aucune donnée trouvée dans le modèle {model_name}\")\n",
                    f"    print(\"Conseil: Ajoutez des données via l'API ou l'admin Django\")\n",
                    f"else:\n",
                    f"    print(\"Données chargées avec succès\")"
                ]
            },
            
            # Cellule de conversion en DataFrame
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Conversion en DataFrame pandas\n",
                    f"if total_count > 0:\n",
                    f"    # Conversion des données en DataFrame\n",
                    f"    data_list = list(queryset.values())\n",
                    f"    df = pd.DataFrame(data_list)\n",
                    f"    \n",
                    f"    print(\"DataFrame créé avec succès\")\n",
                    f"    print(f\"Dimensions: {{df.shape[0]}} lignes × {{df.shape[1]}} colonnes\")\n",
                    f"    print(\"\\nColonnes disponibles:\")\n",
                    f"    for i, col in enumerate(df.columns, 1):\n",
                    f"        print(f\"  {{i:2d}}. {{col}}\")\n",
                    f"else:\n",
                    f"    df = pd.DataFrame()\n",
                    f"    print(\"DataFrame vide créé\")"
                ]
            },
            
            # Cellule d'affichage des données
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Affichage des premières lignes\n",
                    f"if not df.empty:\n",
                    f"    print(\"Aperçu des données (5 premières lignes):\")\n",
                    f"    print(\"=\" * 50)\n",
                    f"    display(df.head())\n",
                    f"    \n",
                    f"    print(\"\\nInformations sur le DataFrame:\")\n",
                    f"    print(\"=\" * 50)\n",
                    f"    df.info()\n",
                    f"    \n",
                    f"    print(\"\\nStatistiques descriptives:\")\n",
                    f"    print(\"=\" * 50)\n",
                    f"    display(df.describe(include='all'))\n",
                    f"else:\n",
                    f"    print(\"Aucune donnée à afficher\")"
                ]
            },
            
            # Cellule d'export CSV
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Export en CSV\n",
                    f"if not df.empty:\n",
                    f"    # Création du nom de fichier avec timestamp\n",
                    f"    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n",
                    f"    filename = f'{model_name.lower()}_{{timestamp}}.csv'\n",
                    f"    filepath = f'../exports/{{filename}}'\n",
                    f"    \n",
                    f"    # Création du dossier exports s'il n'existe pas\n",
                    f"    os.makedirs('../exports', exist_ok=True)\n",
                    f"    \n",
                    f"    # Export du DataFrame\n",
                    f"    df.to_csv(filepath, index=False, encoding='utf-8')\n",
                    f"    \n",
                    f"    print(f\"Données exportées avec succès\")\n",
                    f"    print(f\"Fichier: {{filepath}}\")\n",
                    f"    print(f\"{{len(df)}} lignes exportées\")\n",
                    f"    \n",
                    f"    # Vérification du fichier\n",
                    f"    file_size = os.path.getsize(filepath)\n",
                    f"    print(f\"Taille du fichier: {{file_size:,}} octets\")\n",
                    f"else:\n",
                    f"    print(\"Aucune donnée à exporter\")"
                ]
            },
            
            # Cellule d'analyse exploratoire
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Analyse exploratoire des données\n",
                    f"if not df.empty:\n",
                    f"    print(\"ANALYSE EXPLORATOIRE DES DONNÉES\")\n",
                    f"    print(\"=\" * 40)\n",
                    f"    \n",
                    f"    # Valeurs manquantes\n",
                    f"    missing_values = df.isnull().sum()\n",
                    f"    if missing_values.sum() > 0:\n",
                    f"        print(\"\\nValeurs manquantes:\")\n",
                    f"        for col, count in missing_values[missing_values > 0].items():\n",
                    f"            percentage = (count / len(df)) * 100\n",
                    f"            print(f\"  {{col}}: {{count}} ({{percentage:.1f}}%)\")\n",
                    f"    else:\n",
                    f"        print(\"\\nAucune valeur manquante détectée\")\n",
                    f"    \n",
                    f"    # Types de données\n",
                    f"    print(\"\\nTypes de données:\")\n",
                    f"    for col, dtype in df.dtypes.items():\n",
                    f"        print(f\"  {{col}}: {{dtype}}\")\n",
                    f"    \n",
                    f"    # Doublons\n",
                    f"    duplicates = df.duplicated().sum()\n",
                    f"    if duplicates > 0:\n",
                    f"        print(f\"\\n{{duplicates}} lignes dupliquées détectées\")\n",
                    f"    else:\n",
                    f"        print(\"\\nAucun doublon détecté\")\n",
                    f"else:\n",
                    f"    print(\"Aucune donnée pour l'analyse\")"
                ]
            },
            
            # Cellule de visualisations
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Visualisations\n",
                    f"if not df.empty and len(df) > 1:\n",
                    f"    print(\"VISUALISATIONS\")\n",
                    f"    print(\"=\" * 30)\n",
                    f"    \n",
                    f"    # Configuration des graphiques\n",
                    f"    plt.figure(figsize=(15, 10))\n",
                    f"    \n",
                    f"    # Graphique 1: Distribution des données numériques\n",
                    f"    numeric_cols = df.select_dtypes(include=[np.number]).columns\n",
                    f"    if len(numeric_cols) > 0:\n",
                    f"        plt.subplot(2, 2, 1)\n",
                    f"        df[numeric_cols].hist(bins=20, alpha=0.7)\n",
                    f"        plt.title('Distribution des variables numériques')\n",
                    f"        plt.tight_layout()\n",
                    f"    \n",
                    f"    # Graphique 2: Évolution temporelle (si colonnes de date)\n",
                    f"    date_cols = df.select_dtypes(include=['datetime64']).columns\n",
                    f"    if len(date_cols) > 0 and len(df) > 1:\n",
                    f"        plt.subplot(2, 2, 2)\n",
                    f"        df.set_index(date_cols[0]).resample('D').size().plot()\n",
                    f"        plt.title('Évolution temporelle des enregistrements')\n",
                    f"        plt.xticks(rotation=45)\n",
                    f"    \n",
                    f"    # Graphique 3: Top 10 des valeurs (pour colonnes catégorielles)\n",
                    f"    categorical_cols = df.select_dtypes(include=['object']).columns\n",
                    f"    if len(categorical_cols) > 0:\n",
                    f"        plt.subplot(2, 2, 3)\n",
                    f"        col = categorical_cols[0]\n",
                    f"        top_values = df[col].value_counts().head(10)\n",
                    f"        top_values.plot(kind='bar')\n",
                    f"        plt.title(f'Top 10 - {{col}}')\n",
                    f"        plt.xticks(rotation=45)\n",
                    f"    \n",
                    f"    # Graphique 4: Matrice de corrélation (si variables numériques)\n",
                    f"    if len(numeric_cols) > 1:\n",
                    f"        plt.subplot(2, 2, 4)\n",
                    f"        correlation_matrix = df[numeric_cols].corr()\n",
                    f"        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)\n",
                    f"        plt.title('Matrice de corrélation')\n",
                    f"    \n",
                    f"    plt.tight_layout()\n",
                    f"    plt.show()\n",
                    f"    \n",
                    f"    print(\"Visualisations générées avec succès\")\n",
                    f"else:\n",
                    f"    print(\"Données insuffisantes pour les visualisations\")"
                ]
            },
            
            # Cellule de résumé
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "## Résumé de l'analyse\n",
                    "\n",
                    "### Actions réalisées\n",
                    "1. Chargement des données depuis le modèle Django\n",
                    "2. Conversion en DataFrame pandas\n",
                    "3. Export CSV avec timestamp\n",
                    "4. Analyse exploratoire (valeurs manquantes, doublons, types)\n",
                    "5. Visualisations automatiques\n",
                    "\n",
                    "### Informations du modèle\n",
                    f"- **Modèle**: {model_name}\n",
                    f"- **Description**: {model_config['description']}\n",
                    f"- **Catégorie**: {model_config['category']}\n",
                    f"- **Champs principaux**: {', '.join(model_config['fields'])}\n",
                    "\n",
                    "### Prochaines étapes\n",
                    "1. Analyser les résultats obtenus\n",
                    "2. Identifier les patterns et tendances\n",
                    "3. Créer des visualisations personnalisées\n",
                    "4. Intégrer les insights dans le dashboard\n",
                    "\n",
                    f"*Notebook généré automatiquement le {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"
                ]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "codemirror_mode": {
                    "name": "ipython",
                    "version": 3
                },
                "file_extension": ".py",
                "mimetype": "text/x-python",
                "name": "python",
                "nbconvert_exporter": "python",
                "pygments_lexer": "ipython3",
                "version": "3.12.0"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    return notebook_content

def generate_all_notebooks():
    """Génère tous les notebooks pour tous les modèles"""
    print("GÉNÉRATION DES NOTEBOOKS JUPYTER")
    print("=" * 40)

    # Création du dossier notebooks s'il n'existe pas
    os.makedirs("notebooks", exist_ok=True)

    # Génération d'un notebook par modèle
    for model_name, model_config in MODELS_CONFIG.items():
        print(f"Génération du notebook pour {model_name}...")

        # Création du contenu du notebook
        notebook_content = create_notebook_content(model_name, model_config)

        # Nom du fichier
        filename = f"notebooks/{model_name.lower()}_analysis.ipynb"

        # Sauvegarde du notebook
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(notebook_content, f, indent=2, ensure_ascii=False)

        print(f"   {filename} créé avec succès")

    print(f"\nGÉNÉRATION TERMINÉE")
    print(f"{len(MODELS_CONFIG)} notebooks créés dans le dossier 'notebooks/'")
    print("\nListe des notebooks générés:")
    for i, model_name in enumerate(MODELS_CONFIG.keys(), 1):
        category = MODELS_CONFIG[model_name]['category']
        print(f"  {i:2d}. {model_name.lower()}_analysis.ipynb ({category})")

if __name__ == "__main__":
    generate_all_notebooks()
