# 🧪 GUIDE COMPLET POUR TESTER L'API KAYDAN ANALYTICS HUB

## 🎯 OBJECTIF
Ce guide vous explique comment tester tous les nouveaux endpoints API que nous venons de créer via Swagger UI.

## 🚀 ÉTAPES DE TEST

### 1. 🔐 AUTHENTIFICATION

**Étape 1.1 : Connexion**
1. Ouvrez Swagger UI : http://127.0.0.1:8000/doc/
2. Cherchez l'endpoint `POST /api/auth/login/`
3. Cliquez sur "Try it out"
4. Utilisez ces identifiants :
```json
{
  "username": "testuser",
  "password": "testpass123"
}
```
5. C<PERSON>z sur "Execute"
6. **COPIEZ LE TOKEN** de la réponse (champ "token")

**Étape 1.2 : Configuration de l'authentification**
1. En haut de la page Swagger, cliquez sur le bouton "Authorize" 🔒
2. Dans le champ "Value", tapez : `Token VOTRE_TOKEN_ICI`
   - Exemple : `Token 9944b09199c62bcf9418ad846dd0e4bbdfc6ee4b`
3. Cliquez sur "Authorize"
4. Fermez la popup

## 📊 TESTS DES NOUVEAUX ENDPOINTS

### 2. 💰 DONNÉES PIB (Produit Intérieur Brut)

**Endpoint de base :** `/api/donnees-macroeconomiques/`

**Tests à effectuer :**

**2.1 GET - Liste des données PIB**
- Endpoint : `GET /api/donnees-macroeconomiques/`
- Résultat attendu : Liste de 3 entrées PIB (2021, 2022, 2023)

**2.2 GET - Détail d'une donnée PIB**
- Endpoint : `GET /api/donnees-macroeconomiques/{id}/`
- Utilisez l'ID d'une entrée de la liste précédente

**2.3 POST - Créer une nouvelle donnée PIB**
- Endpoint : `POST /api/donnees-macroeconomiques/`
- Body exemple :
```json
{
  "annee_deb_couv": 2024,
  "mois_deb_couv": 1,
  "annee_fin_couv": 2024,
  "mois_fin_couv": 12,
  "valeur": 48000.7
}
```

**2.4 PUT - Modifier complètement une donnée PIB**
- Endpoint : `PUT /api/donnees-macroeconomiques/{id}/`
- Body : Tous les champs requis

**2.5 PATCH - Modifier partiellement une donnée PIB**
- Endpoint : `PATCH /api/donnees-macroeconomiques/{id}/`
- Body exemple :
```json
{
  "valeur": 49000.0
}
```

**2.6 DELETE - Supprimer une donnée PIB**
- Endpoint : `DELETE /api/donnees-macroeconomiques/{id}/`

**2.7 Actions personnalisées :**
- `GET /api/donnees-macroeconomiques/latest/` - Dernière donnée
- `GET /api/donnees-macroeconomiques/by_year/?annee=2023` - Par année

### 3. 📈 DONNÉES INFLATION

**Endpoint de base :** `/api/donnees-inflation/`

**Tests similaires à PIB + actions spécifiques :**
- `GET /api/donnees-inflation/by_country/?country_code=CIV`
- `GET /api/donnees-inflation/latest_by_country/?country_code=CIV`

**Body exemple pour POST :**
```json
{
  "country_name": "Burkina Faso",
  "country_code": "BFA",
  "indicator_name": "Inflation, consumer prices (annual %)",
  "indicator_code": "FP.CPI.TOTL.ZG",
  "year": 2024,
  "value": 3.8
}
```

### 4. 🏦 TAUX DE PRÊT IMMOBILIER

**Endpoint de base :** `/api/taux-pret-immobilier/`

**Actions spécifiques :**
- `GET /api/taux-pret-immobilier/by_bank/?banque=Société`
- `GET /api/taux-pret-immobilier/by_loan_type/?type_pret=Standard`

**Body exemple pour POST :**
```json
{
  "banque": "Ecobank Côte d'Ivoire",
  "type_pret": "Logement Social",
  "taux_interet": "6.5-7.5",
  "duree_pret": "5-20 ans",
  "frais_dossier": "0.8% (max 100K)",
  "conditions": "Revenus justifiés",
  "source": "Site web banque"
}
```

### 5. 🏠 PRIX AU MÈTRE CARRÉ

**Endpoint de base :** `/api/prix-metre-carre/`

**Actions spécifiques :**
- `GET /api/prix-metre-carre/by_commune/?commune=SONGON`
- `GET /api/prix-metre-carre/price_range/?prix_min=10000&prix_max=20000`

**Body exemple pour POST :**
```json
{
  "commune": "COCODY",
  "prix_min": 25000,
  "prix_max": 120000,
  "prix_moyen": 65000,
  "source": "https://www.sikafinance.com/immo/prix_terrains_lagunes-r2"
}
```

### 6. 🧱 MATÉRIAUX DE CONSTRUCTION

**Endpoint de base :** `/api/materiaux-construction/`

**Actions spécifiques :**
- `GET /api/materiaux-construction/by_category/?categorie=Building`
- `GET /api/materiaux-construction/search/?q=fer`

**Body exemple pour POST :**
```json
{
  "categorie": "Roofing Materials",
  "titre": "Tôle Ondulée Galvanisée",
  "prix": "12500",
  "description": "Tôle ondulée galvanisée 0.4mm, résistante à la corrosion"
}
```

### 7. 👥 PROJECTIONS DÉMOGRAPHIQUES

**Endpoint de base :** `/api/projections-demographiques/`

**Actions spécifiques :**
- `GET /api/projections-demographiques/by_region/?region=Abidjan`
- `GET /api/projections-demographiques/latest_by_region/?region=Abidjan`

**Body exemple pour POST :**
```json
{
  "region": "Yamoussoukro",
  "annee": 2026,
  "population_totale": 450000,
  "population_urbaine": 380000,
  "population_rurale": 70000,
  "taux_croissance": 2.3,
  "densite": 125.8,
  "source": "https://www.ins.ci/projections-demographiques"
}
```

### 8. 🚶 MIGRATIONS INTERNES

**Endpoint de base :** `/api/migrations-internes/`

**Actions spécifiques :**
- `GET /api/migrations-internes/by_origin/?region_origine=Denguélé`
- `GET /api/migrations-internes/by_destination/?region_destination=Abidjan`
- `GET /api/migrations-internes/top_migrations/?limit=5`
- `GET /api/migrations-internes/migration_flows/`

**Body exemple pour POST :**
```json
{
  "annee_reference": 2022,
  "region_origine": "Montagnes",
  "region_destination": "Abidjan",
  "nombre_migrants_total": 15420,
  "source_donnee": "INS - Enquête Migration 2022"
}
```

## ✅ CHECKLIST DE VALIDATION

Pour chaque endpoint, vérifiez :

### Opérations CRUD de base :
- [ ] **GET** (liste) - Retourne toutes les données
- [ ] **GET** (détail) - Retourne une donnée spécifique
- [ ] **POST** - Crée une nouvelle donnée
- [ ] **PUT** - Modifie complètement une donnée
- [ ] **PATCH** - Modifie partiellement une donnée
- [ ] **DELETE** - Supprime une donnée

### Actions personnalisées :
- [ ] Toutes les actions custom fonctionnent
- [ ] Les filtres par paramètres fonctionnent
- [ ] Les validations empêchent les données invalides

### Réponses API :
- [ ] Status codes corrects (200, 201, 400, 404, etc.)
- [ ] Format JSON cohérent
- [ ] Messages d'erreur clairs
- [ ] Pagination si applicable

## 🐛 RÉSOLUTION DE PROBLÈMES

**Erreur 401 Unauthorized :**
- Vérifiez que vous êtes bien authentifié
- Le token doit être au format : `Token YOUR_TOKEN_HERE`

**Erreur 400 Bad Request :**
- Vérifiez le format JSON de votre body
- Respectez les types de données (int, string, etc.)

**Erreur 404 Not Found :**
- Vérifiez que l'ID existe dans la base
- Vérifiez l'URL de l'endpoint

## 🎉 RÉSULTAT ATTENDU

Si tous les tests passent, vous devriez avoir :
- ✅ 7 nouveaux modèles de données fonctionnels
- ✅ 35+ endpoints API opérationnels
- ✅ Opérations CRUD complètes (GET, POST, PUT, PATCH, DELETE)
- ✅ Actions personnalisées avec filtres
- ✅ Validations de données
- ✅ Documentation Swagger complète

**🚀 Votre API Kaydan Analytics Hub est maintenant prête pour l'intégration !**
