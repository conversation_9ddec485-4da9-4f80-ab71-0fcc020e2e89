# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON>, 2014
# <PERSON>, 2013
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2023-04-24 18:05+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Swedish (http://www.transifex.com/django/django/language/"
"sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Sites"
msgstr "Webbplatser"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Domännamnet kan inte innehålla mellanslag eller tab."

msgid "domain name"
msgstr "domännamn"

msgid "display name"
msgstr "visningsnamn"

msgid "site"
msgstr "webbplats"

msgid "sites"
msgstr "webbplatser"
