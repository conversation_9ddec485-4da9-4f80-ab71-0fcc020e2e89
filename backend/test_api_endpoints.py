#!/usr/bin/env python3
"""
Script de test des endpoints API pour les nouveaux modèles
"""

import os
import sys
import django
import json

# Configuration Django
sys.path.append('/home/<USER>/Documents/Kaydan/KAH/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kay_analytics.settings')
django.setup()

from api.models import (
    DonneesProduitInterieurBrut,
    donneesInflation,
    DonnesTauxPretImmobilier,
    DonneesPrixMetreCarre,
    DonneesMateriauxConstruction,
    DonneesMigrationInterne,
    DonneesProjectionDemographique
)

def create_test_data():
    """Crée des données de test pour tous les modèles"""
    
    print("🔧 CRÉATION DES DONNÉES DE TEST")
    print("=" * 50)
    
    # Nettoyer les données existantes
    DonneesProduitInterieurBrut.objects.all().delete()
    donneesInflation.objects.all().delete()
    DonnesTauxPretImmobilier.objects.all().delete()
    DonneesPrixMetreCarre.objects.all().delete()
    DonneesMateriauxConstruction.objects.all().delete()
    DonneesMigrationInterne.objects.all().delete()
    DonneesProjectionDemographique.objects.all().delete()
    
    # Créer des données PIB
    pib_data = [
        {"annee_deb_couv": 2023, "mois_deb_couv": 1, "annee_fin_couv": 2023, "mois_fin_couv": 12, "valeur": 45000.5},
        {"annee_deb_couv": 2022, "mois_deb_couv": 1, "annee_fin_couv": 2022, "mois_fin_couv": 12, "valeur": 42000.3},
        {"annee_deb_couv": 2021, "mois_deb_couv": 1, "annee_fin_couv": 2021, "mois_fin_couv": 12, "valeur": 40000.1},
    ]
    for data in pib_data:
        DonneesProduitInterieurBrut.objects.create(**data)
    print(f"   ✅ PIB: {len(pib_data)} entrées créées")
    
    # Créer des données d'inflation
    inflation_data = [
        {"country_name": "Côte d'Ivoire", "country_code": "CIV", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2023, "value": 3.2},
        {"country_name": "Côte d'Ivoire", "country_code": "CIV", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2022, "value": 2.8},
        {"country_name": "Ghana", "country_code": "GHA", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2023, "value": 4.1},
    ]
    for data in inflation_data:
        donneesInflation.objects.create(**data)
    print(f"   ✅ Inflation: {len(inflation_data)} entrées créées")
    
    # Créer des données de taux de prêt
    taux_data = [
        {"banque": "Société Générale CI", "type_pret": "Standard", "taux_interet": "8.25-9.55", "duree_pret": "3-10 ans", "frais_dossier": "1.1% (max 200K)", "conditions": "Aucune", "source": "Données utilisateur"},
        {"banque": "BACI", "type_pret": "Premium", "taux_interet": "7.5-8.5", "duree_pret": "5-15 ans", "frais_dossier": "1% (max 150K)", "conditions": "Revenus réguliers", "source": "Site web"},
    ]
    for data in taux_data:
        DonnesTauxPretImmobilier.objects.create(**data)
    print(f"   ✅ Taux prêts: {len(taux_data)} entrées créées")
    
    # Créer des données de prix m²
    prix_data = [
        {"commune": "SONGON", "prix_min": 6000, "prix_max": 30000, "prix_moyen": 12162, "source": "https://www.sikafinance.com"},
        {"commune": "BINGERVILLE", "prix_min": 8000, "prix_max": 35000, "prix_moyen": 15000, "source": "https://www.sikafinance.com"},
        {"commune": "ABIDJAN", "prix_min": 15000, "prix_max": 80000, "prix_moyen": 45000, "source": "https://www.sikafinance.com"},
    ]
    for data in prix_data:
        DonneesPrixMetreCarre.objects.create(**data)
    print(f"   ✅ Prix m²: {len(prix_data)} entrées créées")
    
    # Créer des données de matériaux
    materiaux_data = [
        {"categorie": "Building Materials", "titre": "Fer À Béton Chez SKC", "prix": "100", "description": "Fer à béton solide et durable"},
        {"categorie": "Cement", "titre": "Ciment Portland", "prix": "8500", "description": "Ciment de haute qualité"},
        {"categorie": "Blocks", "titre": "Parpaing 15x20x40", "prix": "250", "description": "Parpaing standard"},
    ]
    for data in materiaux_data:
        DonneesMateriauxConstruction.objects.create(**data)
    print(f"   ✅ Matériaux: {len(materiaux_data)} entrées créées")
    
    # Créer des données de migration
    migration_data = [
        {"annee_reference": 2021, "region_origine": "Denguélé", "region_destination": "Abidjan", "nombre_migrants_total": 35890, "source_donnee": "INS - RGPH 2021"},
        {"annee_reference": 2021, "region_origine": "Savanes", "region_destination": "Abidjan", "nombre_migrants_total": 28450, "source_donnee": "INS - RGPH 2021"},
    ]
    for data in migration_data:
        DonneesMigrationInterne.objects.create(**data)
    print(f"   ✅ Migration: {len(migration_data)} entrées créées")
    
    # Créer des données de projection
    projection_data = [
        {"region": "Abidjan", "annee": 2025, "population_totale": 6500000, "population_urbaine": 5850000, "population_rurale": 650000, "taux_croissance": 2.8, "densite": 1250.5, "source": "https://www.ins.ci"},
        {"region": "Bouaké", "annee": 2025, "population_totale": 1200000, "population_urbaine": 800000, "population_rurale": 400000, "taux_croissance": 2.1, "densite": 85.3, "source": "https://www.ins.ci"},
    ]
    for data in projection_data:
        DonneesProjectionDemographique.objects.create(**data)
    print(f"   ✅ Projections: {len(projection_data)} entrées créées")
    
    print("\n🎉 TOUTES LES DONNÉES DE TEST CRÉÉES AVEC SUCCÈS !")

def print_api_endpoints():
    """Affiche tous les endpoints API disponibles"""
    
    print("\n\n📡 ENDPOINTS API DISPONIBLES")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000/api"
    
    endpoints = [
        # PIB
        ("PIB - Liste", "GET", f"{base_url}/donnees-macroeconomiques/"),
        ("PIB - Détail", "GET", f"{base_url}/donnees-macroeconomiques/1/"),
        ("PIB - Créer", "POST", f"{base_url}/donnees-macroeconomiques/"),
        ("PIB - Modifier", "PUT", f"{base_url}/donnees-macroeconomiques/1/"),
        ("PIB - Modifier partiel", "PATCH", f"{base_url}/donnees-macroeconomiques/1/"),
        ("PIB - Supprimer", "DELETE", f"{base_url}/donnees-macroeconomiques/1/"),
        ("PIB - Dernier", "GET", f"{base_url}/donnees-macroeconomiques/latest/"),
        ("PIB - Par année", "GET", f"{base_url}/donnees-macroeconomiques/by_year/?annee=2023"),
        
        # Inflation
        ("Inflation - Liste", "GET", f"{base_url}/donnees-inflation/"),
        ("Inflation - Par pays", "GET", f"{base_url}/donnees-inflation/by_country/?country_code=CIV"),
        ("Inflation - Dernier par pays", "GET", f"{base_url}/donnees-inflation/latest_by_country/?country_code=CIV"),
        
        # Taux prêts
        ("Taux - Liste", "GET", f"{base_url}/taux-pret-immobilier/"),
        ("Taux - Par banque", "GET", f"{base_url}/taux-pret-immobilier/by_bank/?banque=Société"),
        ("Taux - Par type", "GET", f"{base_url}/taux-pret-immobilier/by_loan_type/?type_pret=Standard"),
        
        # Prix m²
        ("Prix - Liste", "GET", f"{base_url}/prix-metre-carre/"),
        ("Prix - Par commune", "GET", f"{base_url}/prix-metre-carre/by_commune/?commune=SONGON"),
        ("Prix - Plage", "GET", f"{base_url}/prix-metre-carre/price_range/?prix_min=10000&prix_max=20000"),
        
        # Matériaux
        ("Matériaux - Liste", "GET", f"{base_url}/materiaux-construction/"),
        ("Matériaux - Par catégorie", "GET", f"{base_url}/materiaux-construction/by_category/?categorie=Building"),
        ("Matériaux - Recherche", "GET", f"{base_url}/materiaux-construction/search/?q=fer"),
        
        # Projections
        ("Projections - Liste", "GET", f"{base_url}/projections-demographiques/"),
        ("Projections - Par région", "GET", f"{base_url}/projections-demographiques/by_region/?region=Abidjan"),
        
        # Migration
        ("Migration - Liste", "GET", f"{base_url}/migrations-internes/"),
        ("Migration - Par origine", "GET", f"{base_url}/migrations-internes/by_origin/?region_origine=Denguélé"),
        ("Migration - Par destination", "GET", f"{base_url}/migrations-internes/by_destination/?region_destination=Abidjan"),
        ("Migration - Top migrations", "GET", f"{base_url}/migrations-internes/top_migrations/?limit=5"),
    ]
    
    for name, method, url in endpoints:
        print(f"   {method:6} | {name:25} | {url}")
    
    print(f"\n📖 DOCUMENTATION SWAGGER: http://127.0.0.1:8000/doc/")
    print(f"🔐 AUTHENTIFICATION: Utilisez Knox Token dans les headers")

if __name__ == "__main__":
    try:
        create_test_data()
        print_api_endpoints()
        
        print("\n\n🚀 INSTRUCTIONS POUR TESTER")
        print("=" * 50)
        print("1. Démarrez le serveur Django:")
        print("   cd backend && source venv/bin/activate && python manage.py runserver")
        print("\n2. Ouvrez Swagger dans votre navigateur:")
        print("   http://127.0.0.1:8000/doc/")
        print("\n3. Authentifiez-vous d'abord via:")
        print("   POST /api/auth/login/")
        print("\n4. Copiez le token et utilisez-le dans l'en-tête:")
        print("   Authorization: Token YOUR_TOKEN_HERE")
        print("\n5. Testez tous les endpoints CRUD (GET, POST, PUT, PATCH, DELETE)")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
