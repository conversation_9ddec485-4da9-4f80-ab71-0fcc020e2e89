#!/usr/bin/env python3
"""
Script de test COMPLET des endpoints API pour les nouveaux modèles
Tests automatisés de tous les CRUD et actions personnalisées
"""

import os
import sys
import django
import json
import requests
import time
from datetime import datetime

# Configuration Django
sys.path.append('/home/<USER>/Documents/Kaydan/KAH/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kay_analytics.settings')
django.setup()

from api.models import (
    DonneesProduitInterieurBrut,
    donneesInflation,
    DonnesTauxPretImmobilier,
    DonneesPrixMetreCarre,
    DonneesMateriauxConstruction,
    DonneesMigrationInterne,
    DonneesProjectionDemographique
)
from accounts.models import CustomUser

# Configuration des tests
BASE_URL = "http://127.0.0.1:8000/api"
TEST_USER = {
    "username": "apitest",
    "password": "apitest123"
}

class APITester:
    def __init__(self):
        self.token = None
        self.headers = {}
        self.test_results = {
            "passed": 0,
            "failed": 0,
            "errors": []
        }
        self.created_objects = {}  # Pour stocker les IDs des objets créés

    def log_test(self, test_name, success, message=""):
        """Enregistre le résultat d'un test"""
        if success:
            self.test_results["passed"] += 1
            print(f"   ✅ {test_name}")
        else:
            self.test_results["failed"] += 1
            self.test_results["errors"].append(f"{test_name}: {message}")
            print(f"   ❌ {test_name}: {message}")

    def authenticate(self):
        """Authentification via l'API"""
        print("\n🔐 AUTHENTIFICATION")
        print("=" * 50)

        try:
            response = requests.post(f"{BASE_URL}/auth/login/", json=TEST_USER)
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("token")
                self.headers = {"Authorization": f"Token {self.token}"}
                self.log_test("Authentification", True)
                return True
            else:
                self.log_test("Authentification", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Authentification", False, str(e))
            return False

    def test_crud_operations(self, endpoint, model_name, create_data, update_data, patch_data):
        """Test complet des opérations CRUD pour un endpoint"""
        print(f"\n📊 TESTS CRUD - {model_name.upper()}")
        print("=" * 50)

        created_id = None

        # 1. GET Liste (avant création)
        try:
            response = requests.get(f"{BASE_URL}/{endpoint}/", headers=self.headers)
            initial_count = len(response.json().get("results", response.json())) if response.status_code == 200 else 0
            self.log_test(f"{model_name} - GET Liste (initial)", response.status_code == 200)
        except Exception as e:
            self.log_test(f"{model_name} - GET Liste (initial)", False, str(e))
            initial_count = 0

        # 2. POST Création
        try:
            response = requests.post(f"{BASE_URL}/{endpoint}/", json=create_data, headers=self.headers)
            if response.status_code == 201:
                created_id = response.json().get("id")
                self.created_objects[model_name] = created_id
                self.log_test(f"{model_name} - POST Création", True)
            else:
                self.log_test(f"{model_name} - POST Création", False, f"Status: {response.status_code}, Response: {response.text[:200]}")
        except Exception as e:
            self.log_test(f"{model_name} - POST Création", False, str(e))

        if not created_id:
            print(f"   ⚠️ Impossible de continuer les tests CRUD pour {model_name} sans ID")
            return

        # 3. GET Détail
        try:
            response = requests.get(f"{BASE_URL}/{endpoint}/{created_id}/", headers=self.headers)
            self.log_test(f"{model_name} - GET Détail", response.status_code == 200)
        except Exception as e:
            self.log_test(f"{model_name} - GET Détail", False, str(e))

        # 4. PUT Modification complète
        try:
            response = requests.put(f"{BASE_URL}/{endpoint}/{created_id}/", json=update_data, headers=self.headers)
            self.log_test(f"{model_name} - PUT Modification", response.status_code == 200)
        except Exception as e:
            self.log_test(f"{model_name} - PUT Modification", False, str(e))

        # 5. PATCH Modification partielle
        try:
            response = requests.patch(f"{BASE_URL}/{endpoint}/{created_id}/", json=patch_data, headers=self.headers)
            self.log_test(f"{model_name} - PATCH Modification", response.status_code == 200)
        except Exception as e:
            self.log_test(f"{model_name} - PATCH Modification", False, str(e))

        # 6. GET Liste (après création)
        try:
            response = requests.get(f"{BASE_URL}/{endpoint}/", headers=self.headers)
            final_count = len(response.json().get("results", response.json())) if response.status_code == 200 else 0
            self.log_test(f"{model_name} - GET Liste (final)", response.status_code == 200 and final_count > initial_count)
        except Exception as e:
            self.log_test(f"{model_name} - GET Liste (final)", False, str(e))

        # 7. DELETE (optionnel - commenté pour garder les données de test)
        # try:
        #     response = requests.delete(f"{BASE_URL}/{endpoint}/{created_id}/", headers=self.headers)
        #     self.log_test(f"{model_name} - DELETE", response.status_code == 204)
        # except Exception as e:
        #     self.log_test(f"{model_name} - DELETE", False, str(e))

    def test_custom_actions(self, endpoint, model_name, actions):
        """Test des actions personnalisées"""
        print(f"\n🎯 ACTIONS PERSONNALISÉES - {model_name.upper()}")
        print("=" * 50)

        for action_name, action_url in actions.items():
            try:
                full_url = f"{BASE_URL}/{endpoint}/{action_url}"
                response = requests.get(full_url, headers=self.headers)
                success = response.status_code in [200, 404]  # 404 acceptable si pas de données
                self.log_test(f"{model_name} - {action_name}", success)
                if not success:
                    print(f"      URL: {full_url}")
                    print(f"      Status: {response.status_code}")
                    print(f"      Response: {response.text[:200]}")
            except Exception as e:
                self.log_test(f"{model_name} - {action_name}", False, str(e))

    def test_pib_endpoints(self):
        """Test complet des endpoints PIB"""
        create_data = {
            "annee_deb_couv": 2024,
            "mois_deb_couv": 1,
            "annee_fin_couv": 2024,
            "mois_fin_couv": 12,
            "valeur": 48000.7
        }
        update_data = {
            "annee_deb_couv": 2024,
            "mois_deb_couv": 1,
            "annee_fin_couv": 2024,
            "mois_fin_couv": 12,
            "valeur": 49000.0
        }
        patch_data = {"valeur": 50000.0}

        self.test_crud_operations("donnees-macroeconomiques", "PIB", create_data, update_data, patch_data)

        actions = {
            "Latest": "latest/",
            "By Year 2023": "by_year/?annee=2023",
            "By Year 2024": "by_year/?annee=2024"
        }
        self.test_custom_actions("donnees-macroeconomiques", "PIB", actions)

    def test_inflation_endpoints(self):
        """Test complet des endpoints Inflation"""
        create_data = {
            "country_name": "Burkina Faso",
            "country_code": "BFA",
            "indicator_name": "Inflation, consumer prices (annual %)",
            "indicator_code": "FP.CPI.TOTL.ZG",
            "year": 2024,
            "value": 3.8
        }
        update_data = {
            "country_name": "Burkina Faso",
            "country_code": "BFA",
            "indicator_name": "Inflation, consumer prices (annual %)",
            "indicator_code": "FP.CPI.TOTL.ZG",
            "year": 2024,
            "value": 4.2
        }
        patch_data = {"value": 4.5}

        self.test_crud_operations("donnees-inflation", "Inflation", create_data, update_data, patch_data)

        actions = {
            "By Country CIV": "by_country/?country_code=CIV",
            "By Country BFA": "by_country/?country_code=BFA",
            "Latest by Country CIV": "latest_by_country/?country_code=CIV",
            "Latest by Country BFA": "latest_by_country/?country_code=BFA"
        }
        self.test_custom_actions("donnees-inflation", "Inflation", actions)

    def test_taux_pret_endpoints(self):
        """Test complet des endpoints Taux de Prêt"""
        create_data = {
            "banque": "Ecobank Côte d'Ivoire",
            "type_pret": "Logement Social",
            "taux_interet": "6.5-7.5",
            "duree_pret": "5-20 ans",
            "frais_dossier": "0.8% (max 100K)",
            "conditions": "Revenus justifiés",
            "source": "Site web banque"
        }
        update_data = {
            "banque": "Ecobank Côte d'Ivoire",
            "type_pret": "Logement Social",
            "taux_interet": "6.0-7.0",
            "duree_pret": "5-20 ans",
            "frais_dossier": "0.8% (max 100K)",
            "conditions": "Revenus justifiés",
            "source": "Site web banque"
        }
        patch_data = {"taux_interet": "5.5-6.5"}

        self.test_crud_operations("taux-pret-immobilier", "TauxPret", create_data, update_data, patch_data)

        actions = {
            "By Bank Société": "by_bank/?banque=Société",
            "By Bank Ecobank": "by_bank/?banque=Ecobank",
            "By Loan Type Standard": "by_loan_type/?type_pret=Standard",
            "By Loan Type Social": "by_loan_type/?type_pret=Social"
        }
        self.test_custom_actions("taux-pret-immobilier", "TauxPret", actions)

    def test_prix_m2_endpoints(self):
        """Test complet des endpoints Prix au m²"""
        create_data = {
            "commune": "COCODY",
            "prix_min": 25000,
            "prix_max": 120000,
            "prix_moyen": 65000,
            "source": "https://www.sikafinance.com/immo/prix_terrains_lagunes-r2"
        }
        update_data = {
            "commune": "COCODY",
            "prix_min": 30000,
            "prix_max": 130000,
            "prix_moyen": 70000,
            "source": "https://www.sikafinance.com/immo/prix_terrains_lagunes-r2"
        }
        patch_data = {"prix_moyen": 75000}

        self.test_crud_operations("prix-metre-carre", "PrixM2", create_data, update_data, patch_data)

        actions = {
            "By Commune SONGON": "by_commune/?commune=SONGON",
            "By Commune COCODY": "by_commune/?commune=COCODY",
            "Price Range 10K-20K": "price_range/?prix_min=10000&prix_max=20000",
            "Price Range 50K-80K": "price_range/?prix_min=50000&prix_max=80000"
        }
        self.test_custom_actions("prix-metre-carre", "PrixM2", actions)

    def test_materiaux_endpoints(self):
        """Test complet des endpoints Matériaux"""
        create_data = {
            "categorie": "Roofing Materials",
            "titre": "Tôle Ondulée Galvanisée",
            "prix": "12500",
            "description": "Tôle ondulée galvanisée 0.4mm, résistante à la corrosion"
        }
        update_data = {
            "categorie": "Roofing Materials",
            "titre": "Tôle Ondulée Galvanisée Premium",
            "prix": "13500",
            "description": "Tôle ondulée galvanisée 0.5mm, résistante à la corrosion"
        }
        patch_data = {"prix": "14000"}

        self.test_crud_operations("materiaux-construction", "Materiaux", create_data, update_data, patch_data)

        actions = {
            "By Category Building": "by_category/?categorie=Building",
            "By Category Roofing": "by_category/?categorie=Roofing",
            "Search Fer": "search/?q=fer",
            "Search Tôle": "search/?q=tôle",
            "Search Ciment": "search/?q=ciment"
        }
        self.test_custom_actions("materiaux-construction", "Materiaux", actions)

    def test_projections_endpoints(self):
        """Test complet des endpoints Projections Démographiques"""
        create_data = {
            "region": "Yamoussoukro",
            "annee": 2026,
            "population_totale": 450000,
            "population_urbaine": 380000,
            "population_rurale": 70000,
            "taux_croissance": 2.3,
            "densite": 125.8,
            "source": "https://www.ins.ci/projections-demographiques"
        }
        update_data = {
            "region": "Yamoussoukro",
            "annee": 2026,
            "population_totale": 460000,
            "population_urbaine": 390000,
            "population_rurale": 70000,
            "taux_croissance": 2.5,
            "densite": 130.0,
            "source": "https://www.ins.ci/projections-demographiques"
        }
        patch_data = {"population_totale": 470000}

        self.test_crud_operations("projections-demographiques", "Projections", create_data, update_data, patch_data)

        actions = {
            "By Region Abidjan": "by_region/?region=Abidjan",
            "By Region Yamoussoukro": "by_region/?region=Yamoussoukro",
            "By Region Bouaké": "by_region/?region=Bouaké",
            "Latest by Region Abidjan": "latest_by_region/?region=Abidjan"
        }
        self.test_custom_actions("projections-demographiques", "Projections", actions)

    def test_migration_endpoints(self):
        """Test complet des endpoints Migration Interne"""
        create_data = {
            "annee_reference": 2022,
            "region_origine": "Montagnes",
            "region_destination": "Abidjan",
            "nombre_migrants_total": 15420,
            "source_donnee": "INS - Enquête Migration 2022"
        }
        update_data = {
            "annee_reference": 2022,
            "region_origine": "Montagnes",
            "region_destination": "Abidjan",
            "nombre_migrants_total": 16000,
            "source_donnee": "INS - Enquête Migration 2022 (Révisé)"
        }
        patch_data = {"nombre_migrants_total": 16500}

        self.test_crud_operations("migrations-internes", "Migration", create_data, update_data, patch_data)

        actions = {
            "By Origin Denguélé": "by_origin/?region_origine=Denguélé",
            "By Origin Montagnes": "by_origin/?region_origine=Montagnes",
            "By Destination Abidjan": "by_destination/?region_destination=Abidjan",
            "Top Migrations 5": "top_migrations/?limit=5",
            "Top Migrations 10": "top_migrations/?limit=10",
            "Migration Flows": "migration_flows/"
        }
        self.test_custom_actions("migrations-internes", "Migration", actions)

    def run_all_tests(self):
        """Exécute tous les tests"""
        print("🚀 DÉBUT DES TESTS COMPLETS DE L'API")
        print("=" * 70)
        print(f"Base URL: {BASE_URL}")
        print(f"Utilisateur de test: {TEST_USER['username']}")
        print(f"Heure de début: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Authentification
        if not self.authenticate():
            print("❌ Impossible de continuer sans authentification")
            return

        # Tests de tous les modèles
        self.test_pib_endpoints()
        self.test_inflation_endpoints()
        self.test_taux_pret_endpoints()
        self.test_prix_m2_endpoints()
        self.test_materiaux_endpoints()
        self.test_projections_endpoints()
        self.test_migration_endpoints()

        # Résultats finaux
        self.print_final_results()

    def print_final_results(self):
        """Affiche les résultats finaux"""
        print("\n" + "=" * 70)
        print("📊 RÉSULTATS FINAUX DES TESTS")
        print("=" * 70)

        total_tests = self.test_results["passed"] + self.test_results["failed"]
        success_rate = (self.test_results["passed"] / total_tests * 100) if total_tests > 0 else 0

        print(f"✅ Tests réussis: {self.test_results['passed']}")
        print(f"❌ Tests échoués: {self.test_results['failed']}")
        print(f"📈 Taux de réussite: {success_rate:.1f}%")
        print(f"🕒 Heure de fin: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        if self.test_results["errors"]:
            print(f"\n🔍 DÉTAILS DES ERREURS ({len(self.test_results['errors'])}):")
            print("-" * 50)
            for i, error in enumerate(self.test_results["errors"], 1):
                print(f"{i:2d}. {error}")

        if self.created_objects:
            print(f"\n🆔 OBJETS CRÉÉS PENDANT LES TESTS:")
            print("-" * 50)
            for model, obj_id in self.created_objects.items():
                print(f"   {model}: ID {obj_id}")

        # Recommandations
        print(f"\n💡 RECOMMANDATIONS:")
        print("-" * 50)
        if success_rate >= 90:
            print("🎉 Excellent ! Votre API est prête pour la production.")
        elif success_rate >= 75:
            print("👍 Bon travail ! Quelques ajustements mineurs nécessaires.")
        elif success_rate >= 50:
            print("⚠️ Des améliorations importantes sont nécessaires.")
        else:
            print("🚨 L'API nécessite des corrections majeures.")

        print(f"\n📖 Pour plus de détails, consultez la documentation Swagger:")
        print(f"   {BASE_URL.replace('/api', '/doc/')}")


def create_comprehensive_test_data():
    """Crée des données de test complètes pour tous les modèles"""
    print("\n🔧 CRÉATION DES DONNÉES DE TEST COMPLÈTES")
    print("=" * 50)

    # Nettoyer toutes les données existantes
    models_to_clean = [
        DonneesProduitInterieurBrut,
        donneesInflation,
        DonnesTauxPretImmobilier,
        DonneesPrixMetreCarre,
        DonneesMateriauxConstruction,
        DonneesMigrationInterne,
        DonneesProjectionDemographique
    ]

    for model in models_to_clean:
        count = model.objects.count()
        model.objects.all().delete()
        print(f"   🗑️ {model.__name__}: {count} entrées supprimées")

    # Créer des données PIB variées
    pib_data = [
        {"annee_deb_couv": 2020, "mois_deb_couv": 1, "annee_fin_couv": 2020, "mois_fin_couv": 12, "valeur": 38500.2},
        {"annee_deb_couv": 2021, "mois_deb_couv": 1, "annee_fin_couv": 2021, "mois_fin_couv": 12, "valeur": 40000.1},
        {"annee_deb_couv": 2022, "mois_deb_couv": 1, "annee_fin_couv": 2022, "mois_fin_couv": 12, "valeur": 42000.3},
        {"annee_deb_couv": 2023, "mois_deb_couv": 1, "annee_fin_couv": 2023, "mois_fin_couv": 12, "valeur": 45000.5},
        {"annee_deb_couv": 2023, "mois_deb_couv": 1, "annee_fin_couv": 2023, "mois_fin_couv": 6, "valeur": 22000.8},
    ]
    for data in pib_data:
        DonneesProduitInterieurBrut.objects.create(**data)
    print(f"   ✅ PIB: {len(pib_data)} entrées créées")

    # Créer des données d'inflation variées
    inflation_data = [
        {"country_name": "Côte d'Ivoire", "country_code": "CIV", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2020, "value": 2.4},
        {"country_name": "Côte d'Ivoire", "country_code": "CIV", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2021, "value": 4.1},
        {"country_name": "Côte d'Ivoire", "country_code": "CIV", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2022, "value": 2.8},
        {"country_name": "Côte d'Ivoire", "country_code": "CIV", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2023, "value": 3.2},
        {"country_name": "Ghana", "country_code": "GHA", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2023, "value": 4.1},
        {"country_name": "Sénégal", "country_code": "SEN", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2023, "value": 3.8},
    ]
    for data in inflation_data:
        donneesInflation.objects.create(**data)
    print(f"   ✅ Inflation: {len(inflation_data)} entrées créées")

    # Créer des données de taux de prêt variées
    taux_data = [
        {"banque": "Société Générale CI", "type_pret": "Standard", "taux_interet": "8.25-9.55", "duree_pret": "3-10 ans", "frais_dossier": "1.1% (max 200K)", "conditions": "Aucune", "source": "Données utilisateur"},
        {"banque": "BACI", "type_pret": "Premium", "taux_interet": "7.5-8.5", "duree_pret": "5-15 ans", "frais_dossier": "1% (max 150K)", "conditions": "Revenus réguliers", "source": "Site web"},
        {"banque": "UBA Côte d'Ivoire", "type_pret": "Social", "taux_interet": "6.0-7.0", "duree_pret": "10-25 ans", "frais_dossier": "0.5%", "conditions": "Logement social", "source": "Agence"},
        {"banque": "SGBCI", "type_pret": "Standard", "taux_interet": "8.0-9.0", "duree_pret": "5-12 ans", "frais_dossier": "1.2%", "conditions": "Apport 20%", "source": "Site web"},
    ]
    for data in taux_data:
        DonnesTauxPretImmobilier.objects.create(**data)
    print(f"   ✅ Taux prêts: {len(taux_data)} entrées créées")

    # Créer des données de prix m² variées
    prix_data = [
        {"commune": "SONGON", "prix_min": 6000, "prix_max": 30000, "prix_moyen": 12162, "source": "https://www.sikafinance.com"},
        {"commune": "BINGERVILLE", "prix_min": 8000, "prix_max": 35000, "prix_moyen": 15000, "source": "https://www.sikafinance.com"},
        {"commune": "ABIDJAN", "prix_min": 15000, "prix_max": 80000, "prix_moyen": 45000, "source": "https://www.sikafinance.com"},
        {"commune": "GRAND-BASSAM", "prix_min": 12000, "prix_max": 50000, "prix_moyen": 25000, "source": "https://www.sikafinance.com"},
        {"commune": "ANYAMA", "prix_min": 7000, "prix_max": 25000, "prix_moyen": 14000, "source": "https://www.sikafinance.com"},
    ]
    for data in prix_data:
        DonneesPrixMetreCarre.objects.create(**data)
    print(f"   ✅ Prix m²: {len(prix_data)} entrées créées")

    # Créer des données de matériaux variées
    materiaux_data = [
        {"categorie": "Building Materials", "titre": "Fer À Béton Chez SKC", "prix": "100", "description": "Fer à béton solide et durable"},
        {"categorie": "Cement", "titre": "Ciment Portland", "prix": "8500", "description": "Ciment de haute qualité"},
        {"categorie": "Blocks", "titre": "Parpaing 15x20x40", "prix": "250", "description": "Parpaing standard"},
        {"categorie": "Roofing", "titre": "Tôle Ondulée", "prix": "12000", "description": "Tôle galvanisée résistante"},
        {"categorie": "Sand", "titre": "Sable de Rivière", "prix": "15000", "description": "Sable fin pour construction"},
        {"categorie": "Gravel", "titre": "Gravier 5/15", "prix": "18000", "description": "Gravier calibré pour béton"},
    ]
    for data in materiaux_data:
        DonneesMateriauxConstruction.objects.create(**data)
    print(f"   ✅ Matériaux: {len(materiaux_data)} entrées créées")

    # Créer des données de migration variées
    migration_data = [
        {"annee_reference": 2021, "region_origine": "Denguélé", "region_destination": "Abidjan", "nombre_migrants_total": 35890, "source_donnee": "INS - RGPH 2021"},
        {"annee_reference": 2021, "region_origine": "Savanes", "region_destination": "Abidjan", "nombre_migrants_total": 28450, "source_donnee": "INS - RGPH 2021"},
        {"annee_reference": 2021, "region_origine": "Montagnes", "region_destination": "Abidjan", "nombre_migrants_total": 22100, "source_donnee": "INS - RGPH 2021"},
        {"annee_reference": 2021, "region_origine": "Zanzan", "region_destination": "Bouaké", "nombre_migrants_total": 15600, "source_donnee": "INS - RGPH 2021"},
        {"annee_reference": 2021, "region_origine": "Bafing", "region_destination": "Yamoussoukro", "nombre_migrants_total": 12300, "source_donnee": "INS - RGPH 2021"},
    ]
    for data in migration_data:
        DonneesMigrationInterne.objects.create(**data)
    print(f"   ✅ Migration: {len(migration_data)} entrées créées")

    # Créer des données de projection variées
    projection_data = [
        {"region": "Abidjan", "annee": 2025, "population_totale": 6500000, "population_urbaine": 5850000, "population_rurale": 650000, "taux_croissance": 2.8, "densite": 1250.5, "source": "https://www.ins.ci"},
        {"region": "Bouaké", "annee": 2025, "population_totale": 1200000, "population_urbaine": 800000, "population_rurale": 400000, "taux_croissance": 2.1, "densite": 85.3, "source": "https://www.ins.ci"},
        {"region": "Yamoussoukro", "annee": 2025, "population_totale": 400000, "population_urbaine": 320000, "population_rurale": 80000, "taux_croissance": 2.5, "densite": 120.0, "source": "https://www.ins.ci"},
        {"region": "San-Pédro", "annee": 2025, "population_totale": 350000, "population_urbaine": 250000, "population_rurale": 100000, "taux_croissance": 1.8, "densite": 95.2, "source": "https://www.ins.ci"},
    ]
    for data in projection_data:
        DonneesProjectionDemographique.objects.create(**data)
    print(f"   ✅ Projections: {len(projection_data)} entrées créées")

    print(f"\n🎉 TOUTES LES DONNÉES DE TEST CRÉÉES AVEC SUCCÈS !")
    total_created = len(pib_data) + len(inflation_data) + len(taux_data) + len(prix_data) + len(materiaux_data) + len(migration_data) + len(projection_data)
    print(f"📊 Total: {total_created} entrées créées dans 7 modèles")


def check_server_status():
    """Vérifie si le serveur Django est en cours d'exécution"""
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        return True
    except:
        return False


if __name__ == "__main__":
    print("🧪 TESTS COMPLETS DE L'API KAYDAN ANALYTICS HUB")
    print("=" * 70)

    # Vérifier si le serveur est en cours d'exécution
    if not check_server_status():
        print("❌ ERREUR: Le serveur Django n'est pas en cours d'exécution")
        print("💡 Démarrez le serveur avec: python manage.py runserver")
        sys.exit(1)

    try:
        # Créer des données de test complètes
        create_comprehensive_test_data()

        # Exécuter tous les tests
        tester = APITester()
        tester.run_all_tests()

        print(f"\n🏁 TESTS TERMINÉS")
        print("=" * 70)

    except KeyboardInterrupt:
        print(f"\n⚠️ Tests interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()

def create_test_data():
    """Crée des données de test pour tous les modèles"""
    
    print("🔧 CRÉATION DES DONNÉES DE TEST")
    print("=" * 50)
    
    # Nettoyer les données existantes
    DonneesProduitInterieurBrut.objects.all().delete()
    donneesInflation.objects.all().delete()
    DonnesTauxPretImmobilier.objects.all().delete()
    DonneesPrixMetreCarre.objects.all().delete()
    DonneesMateriauxConstruction.objects.all().delete()
    DonneesMigrationInterne.objects.all().delete()
    DonneesProjectionDemographique.objects.all().delete()
    
    # Créer des données PIB
    pib_data = [
        {"annee_deb_couv": 2023, "mois_deb_couv": 1, "annee_fin_couv": 2023, "mois_fin_couv": 12, "valeur": 45000.5},
        {"annee_deb_couv": 2022, "mois_deb_couv": 1, "annee_fin_couv": 2022, "mois_fin_couv": 12, "valeur": 42000.3},
        {"annee_deb_couv": 2021, "mois_deb_couv": 1, "annee_fin_couv": 2021, "mois_fin_couv": 12, "valeur": 40000.1},
    ]
    for data in pib_data:
        DonneesProduitInterieurBrut.objects.create(**data)
    print(f"   ✅ PIB: {len(pib_data)} entrées créées")
    
    # Créer des données d'inflation
    inflation_data = [
        {"country_name": "Côte d'Ivoire", "country_code": "CIV", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2023, "value": 3.2},
        {"country_name": "Côte d'Ivoire", "country_code": "CIV", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2022, "value": 2.8},
        {"country_name": "Ghana", "country_code": "GHA", "indicator_name": "Inflation", "indicator_code": "FP.CPI.TOTL.ZG", "year": 2023, "value": 4.1},
    ]
    for data in inflation_data:
        donneesInflation.objects.create(**data)
    print(f"   ✅ Inflation: {len(inflation_data)} entrées créées")
    
    # Créer des données de taux de prêt
    taux_data = [
        {"banque": "Société Générale CI", "type_pret": "Standard", "taux_interet": "8.25-9.55", "duree_pret": "3-10 ans", "frais_dossier": "1.1% (max 200K)", "conditions": "Aucune", "source": "Données utilisateur"},
        {"banque": "BACI", "type_pret": "Premium", "taux_interet": "7.5-8.5", "duree_pret": "5-15 ans", "frais_dossier": "1% (max 150K)", "conditions": "Revenus réguliers", "source": "Site web"},
    ]
    for data in taux_data:
        DonnesTauxPretImmobilier.objects.create(**data)
    print(f"   ✅ Taux prêts: {len(taux_data)} entrées créées")
    
    # Créer des données de prix m²
    prix_data = [
        {"commune": "SONGON", "prix_min": 6000, "prix_max": 30000, "prix_moyen": 12162, "source": "https://www.sikafinance.com"},
        {"commune": "BINGERVILLE", "prix_min": 8000, "prix_max": 35000, "prix_moyen": 15000, "source": "https://www.sikafinance.com"},
        {"commune": "ABIDJAN", "prix_min": 15000, "prix_max": 80000, "prix_moyen": 45000, "source": "https://www.sikafinance.com"},
    ]
    for data in prix_data:
        DonneesPrixMetreCarre.objects.create(**data)
    print(f"   ✅ Prix m²: {len(prix_data)} entrées créées")
    
    # Créer des données de matériaux
    materiaux_data = [
        {"categorie": "Building Materials", "titre": "Fer À Béton Chez SKC", "prix": "100", "description": "Fer à béton solide et durable"},
        {"categorie": "Cement", "titre": "Ciment Portland", "prix": "8500", "description": "Ciment de haute qualité"},
        {"categorie": "Blocks", "titre": "Parpaing 15x20x40", "prix": "250", "description": "Parpaing standard"},
    ]
    for data in materiaux_data:
        DonneesMateriauxConstruction.objects.create(**data)
    print(f"   ✅ Matériaux: {len(materiaux_data)} entrées créées")
    
    # Créer des données de migration
    migration_data = [
        {"annee_reference": 2021, "region_origine": "Denguélé", "region_destination": "Abidjan", "nombre_migrants_total": 35890, "source_donnee": "INS - RGPH 2021"},
        {"annee_reference": 2021, "region_origine": "Savanes", "region_destination": "Abidjan", "nombre_migrants_total": 28450, "source_donnee": "INS - RGPH 2021"},
    ]
    for data in migration_data:
        DonneesMigrationInterne.objects.create(**data)
    print(f"   ✅ Migration: {len(migration_data)} entrées créées")
    
    # Créer des données de projection
    projection_data = [
        {"region": "Abidjan", "annee": 2025, "population_totale": 6500000, "population_urbaine": 5850000, "population_rurale": 650000, "taux_croissance": 2.8, "densite": 1250.5, "source": "https://www.ins.ci"},
        {"region": "Bouaké", "annee": 2025, "population_totale": 1200000, "population_urbaine": 800000, "population_rurale": 400000, "taux_croissance": 2.1, "densite": 85.3, "source": "https://www.ins.ci"},
    ]
    for data in projection_data:
        DonneesProjectionDemographique.objects.create(**data)
    print(f"   ✅ Projections: {len(projection_data)} entrées créées")
    
    print("\n🎉 TOUTES LES DONNÉES DE TEST CRÉÉES AVEC SUCCÈS !")

def print_api_endpoints():
    """Affiche tous les endpoints API disponibles"""
    
    print("\n\n📡 ENDPOINTS API DISPONIBLES")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000/api"
    
    endpoints = [
        # PIB
        ("PIB - Liste", "GET", f"{base_url}/donnees-macroeconomiques/"),
        ("PIB - Détail", "GET", f"{base_url}/donnees-macroeconomiques/1/"),
        ("PIB - Créer", "POST", f"{base_url}/donnees-macroeconomiques/"),
        ("PIB - Modifier", "PUT", f"{base_url}/donnees-macroeconomiques/1/"),
        ("PIB - Modifier partiel", "PATCH", f"{base_url}/donnees-macroeconomiques/1/"),
        ("PIB - Supprimer", "DELETE", f"{base_url}/donnees-macroeconomiques/1/"),
        ("PIB - Dernier", "GET", f"{base_url}/donnees-macroeconomiques/latest/"),
        ("PIB - Par année", "GET", f"{base_url}/donnees-macroeconomiques/by_year/?annee=2023"),
        
        # Inflation
        ("Inflation - Liste", "GET", f"{base_url}/donnees-inflation/"),
        ("Inflation - Par pays", "GET", f"{base_url}/donnees-inflation/by_country/?country_code=CIV"),
        ("Inflation - Dernier par pays", "GET", f"{base_url}/donnees-inflation/latest_by_country/?country_code=CIV"),
        
        # Taux prêts
        ("Taux - Liste", "GET", f"{base_url}/taux-pret-immobilier/"),
        ("Taux - Par banque", "GET", f"{base_url}/taux-pret-immobilier/by_bank/?banque=Société"),
        ("Taux - Par type", "GET", f"{base_url}/taux-pret-immobilier/by_loan_type/?type_pret=Standard"),
        
        # Prix m²
        ("Prix - Liste", "GET", f"{base_url}/prix-metre-carre/"),
        ("Prix - Par commune", "GET", f"{base_url}/prix-metre-carre/by_commune/?commune=SONGON"),
        ("Prix - Plage", "GET", f"{base_url}/prix-metre-carre/price_range/?prix_min=10000&prix_max=20000"),
        
        # Matériaux
        ("Matériaux - Liste", "GET", f"{base_url}/materiaux-construction/"),
        ("Matériaux - Par catégorie", "GET", f"{base_url}/materiaux-construction/by_category/?categorie=Building"),
        ("Matériaux - Recherche", "GET", f"{base_url}/materiaux-construction/search/?q=fer"),
        
        # Projections
        ("Projections - Liste", "GET", f"{base_url}/projections-demographiques/"),
        ("Projections - Par région", "GET", f"{base_url}/projections-demographiques/by_region/?region=Abidjan"),
        
        # Migration
        ("Migration - Liste", "GET", f"{base_url}/migrations-internes/"),
        ("Migration - Par origine", "GET", f"{base_url}/migrations-internes/by_origin/?region_origine=Denguélé"),
        ("Migration - Par destination", "GET", f"{base_url}/migrations-internes/by_destination/?region_destination=Abidjan"),
        ("Migration - Top migrations", "GET", f"{base_url}/migrations-internes/top_migrations/?limit=5"),
    ]
    
    for name, method, url in endpoints:
        print(f"   {method:6} | {name:25} | {url}")
    
    print(f"\n📖 DOCUMENTATION SWAGGER: http://127.0.0.1:8000/doc/")
    print(f"🔐 AUTHENTIFICATION: Utilisez Knox Token dans les headers")

if __name__ == "__main__":
    try:
        create_test_data()
        print_api_endpoints()
        
        print("\n\n🚀 INSTRUCTIONS POUR TESTER")
        print("=" * 50)
        print("1. Démarrez le serveur Django:")
        print("   cd backend && source venv/bin/activate && python manage.py runserver")
        print("\n2. Ouvrez Swagger dans votre navigateur:")
        print("   http://127.0.0.1:8000/doc/")
        print("\n3. Authentifiez-vous d'abord via:")
        print("   POST /api/auth/login/")
        print("\n4. Copiez le token et utilisez-le dans l'en-tête:")
        print("   Authorization: Token YOUR_TOKEN_HERE")
        print("\n5. Testez tous les endpoints CRUD (GET, POST, PUT, PATCH, DELETE)")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
