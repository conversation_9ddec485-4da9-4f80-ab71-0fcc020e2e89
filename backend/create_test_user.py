#!/usr/bin/env python3
"""
Script pour créer un utilisateur de test
"""

import os
import sys
import django

# Configuration Django
sys.path.append('/home/<USER>/Documents/Kaydan/KAH/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kay_analytics.settings')
django.setup()

from accounts.models import CustomUser

def create_test_user():
    """Crée un utilisateur de test"""
    
    print("👤 CRÉATION D'UN UTILISATEUR DE TEST")
    print("=" * 50)
    
    # Supprimer l'utilisateur existant s'il existe
    try:
        existing_user = CustomUser.objects.get(username='testuser')
        existing_user.delete()
        print("   🗑️ Utilisateur existant supprimé")
    except CustomUser.DoesNotExist:
        pass
    
    # Créer un nouvel utilisateur
    user = CustomUser.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='testpass123',
        first_name='Test',
        last_name='User',
        kaydan_subsidiary='Kaydan Real Estate'
    )
    
    print(f"   ✅ Utilisateur créé: {user.username}")
    print(f"   📧 Email: {user.email}")
    print(f"   🔑 Mot de passe: testpass123")
    print(f"   🏢 Filiale: {user.kaydan_subsidiary}")
    
    return user

if __name__ == "__main__":
    try:
        user = create_test_user()
        
        print("\n🚀 INSTRUCTIONS POUR TESTER L'API")
        print("=" * 50)
        print("1. Démarrez le serveur:")
        print("   python manage.py runserver")
        print("\n2. Connectez-vous via l'API:")
        print("   POST http://127.0.0.1:8000/api/auth/login/")
        print("   Body: {")
        print('     "username": "testuser",')
        print('     "password": "testpass123"')
        print("   }")
        print("\n3. Utilisez le token retourné dans vos requêtes:")
        print("   Authorization: Token YOUR_TOKEN_HERE")
        print("\n4. Ouvrez Swagger:")
        print("   http://127.0.0.1:8000/doc/")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
