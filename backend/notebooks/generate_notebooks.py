#!/usr/bin/env python
"""
Script pour générer automatiquement des notebooks pour chaque modèle Django
"""
import os
import sys
import json
import pandas as pd
import django
from pathlib import Path

# Configurer l'environnement Django
sys.path.append(os.path.abspath('..'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Importer tous les modèles
from api.models import (
    UserProfile,
    DQEData,
    GStockApprovisionnement,
    GStockSortie,
    GStockConsommation,
    GStockAchat,
    GProjet,
    EcoleTalents,
    DonneesProduitInterieurBrut,
    donneesInflation,
    DonnesTauxPretImmobilier,
    DonneesPrixMetreCarre,
    DonneesMateriauxConstruction,
    DonneesProjectionDemographique,
    DonneesMigrationInterne,
)

# Créer les dossiers pour les notebooks
NOTEBOOK_CATEGORIES = {
    'utilisateurs': [UserProfile],
    'dqe': [DQEData],
    'gstock': [GStockApprovisionnement, GStockSortie, GStockConsommation, GStockAchat],
    'gprojet': [GProjet],
    'ecole_talents': [EcoleTalents],
    'macroeconomiques': [DonneesProduitInterieurBrut, donneesInflation, DonnesTauxPretImmobilier],
    'immobilier': [DonneesPrixMetreCarre, DonneesMateriauxConstruction],
    'demographiques': [DonneesProjectionDemographique, DonneesMigrationInterne],
}

# Créer les dossiers
for category in NOTEBOOK_CATEGORIES:
    os.makedirs(category, exist_ok=True)
    print(f"✅ Dossier '{category}' créé")

# Créer le dossier utils s'il n'existe pas
os.makedirs('utils', exist_ok=True)
print("✅ Dossier 'utils' créé")

# Créer le module django_setup.py dans le dossier utils
with open('utils/django_setup.py', 'w') as f:
    f.write('''"""
Utilitaires pour configurer Django dans les notebooks Jupyter
"""
import os
import sys
import django
import pandas as pd
from pathlib import Path

def setup_django():
    """Configure l'environnement Django pour les notebooks"""
    # Ajouter le chemin du projet Django
    sys.path.append(os.path.abspath('../..'))
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
    django.setup()
    print("✅ Environnement Django configuré avec succès")

def model_to_dataframe(model_class, filter_kwargs=None):
    """
    Convertit un modèle Django en DataFrame pandas
    
    Args:
        model_class: Classe du modèle Django
        filter_kwargs: Filtres à appliquer (dict)
        
    Returns:
        DataFrame pandas
    """
    if filter_kwargs is None:
        filter_kwargs = {}
    
    queryset = model_class.objects.filter(**filter_kwargs)
    df = pd.DataFrame(list(queryset.values()))
    
    print(f"✅ {len(df)} enregistrements chargés depuis {model_class.__name__}")
    return df

def save_dataframe_to_model(df, model_class, unique_fields=None):
    """
    Sauvegarde un DataFrame dans un modèle Django
    
    Args:
        df: DataFrame pandas
        model_class: Classe du modèle Django
        unique_fields: Liste des champs pour vérifier les doublons
        
    Returns:
        Tuple (created_count, updated_count)
    """
    created_count = 0
    updated_count = 0
    
    for _, row in df.iterrows():
        data_dict = row.to_dict()
        
        # Supprimer les colonnes qui ne correspondent pas aux champs du modèle
        model_fields = [f.name for f in model_class._meta.fields]
        for key in list(data_dict.keys()):
            if key not in model_fields:
                del data_dict[key]
        
        # Vérifier si l'enregistrement existe déjà
        if unique_fields:
            filter_kwargs = {field: data_dict[field] for field in unique_fields if field in data_dict}
            obj, created = model_class.objects.update_or_create(
                defaults=data_dict,
                **filter_kwargs
            )
            if created:
                created_count += 1
            else:
                updated_count += 1
        else:
            model_class.objects.create(**data_dict)
            created_count += 1
    
    print(f"✅ {created_count} enregistrements créés, {updated_count} mis à jour dans {model_class.__name__}")
    return created_count, updated_count

def ensure_data_dir():
    """Crée les répertoires de données s'ils n'existent pas"""
    data_dir = Path('../../data')
    (data_dir / 'exports').mkdir(parents=True, exist_ok=True)
    (data_dir / 'imports').mkdir(parents=True, exist_ok=True)
    (data_dir / 'processed').mkdir(parents=True, exist_ok=True)
    print("✅ Répertoires de données créés")
''')
print("✅ Module 'utils/django_setup.py' créé")

# Fonction pour générer un notebook pour un modèle
def generate_notebook(model_class, category):
    model_name = model_class.__name__
    file_name = model_name.lower()
    
    # Créer le contenu du notebook
    notebook = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    f"# Analyse des données {model_name}\n",
                    "\n",
                    "Ce notebook permet de:\n",
                    "1. Charger les données {model_name} depuis la base de données\n",
                    "2. Effectuer des transformations et analyses\n",
                    "3. Exporter les données vers des fichiers CSV\n",
                    "4. Visualiser les tendances"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Import des utilitaires\n",
                    "import sys\n",
                    "import os\n",
                    "sys.path.append('../utils')\n",
                    "from django_setup import setup_django, model_to_dataframe, save_dataframe_to_model, ensure_data_dir\n",
                    "\n",
                    "# Configuration de l'environnement Django\n",
                    "setup_django()\n",
                    "ensure_data_dir()\n",
                    "\n",
                    "# Imports standards pour l'analyse de données\n",
                    "import pandas as pd\n",
                    "import numpy as np\n",
                    "import matplotlib.pyplot as plt\n",
                    "import seaborn as sns\n",
                    "from datetime import datetime, timedelta\n",
                    "\n",
                    "# Configuration des graphiques\n",
                    "plt.style.use('ggplot')\n",
                    "sns.set_theme()\n",
                    "%matplotlib inline"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    f"# Import du modèle Django\n",
                    f"from api.models import {model_name}\n",
                    "\n",
                    "# Récupération des données\n",
                    f"df = model_to_dataframe({model_name})\n",
                    "df.head()"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Informations sur le DataFrame\n",
                    "print(f\"Nombre d'enregistrements: {len(df)}\")\n",
                    "print(\"\\nTypes de données:\")\n",
                    "print(df.dtypes)\n",
                    "print(\"\\nStatistiques descriptives:\")\n",
                    "df.describe(include='all')"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Vérification des valeurs manquantes\n",
                    "missing_values = df.isnull().sum()\n",
                    "print(\"Valeurs manquantes par colonne:\")\n",
                    "for col, count in missing_values.items():\n",
                    "    if count > 0:\n",
                    "        print(f\"{col}: {count} ({count/len(df):.2%})\")"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Analyse et visualisation des données\n",
                    "if 'created_at' in df.columns:\n",
                    "    # Analyse temporelle\n",
                    "    df['created_at'] = pd.to_datetime(df['created_at'])\n",
                    "    plt.figure(figsize=(12, 6))\n",
                    "    df.set_index('created_at').resample('M').size().plot(kind='line', marker='o')\n",
                    "    plt.title(f'Évolution des entrées {model_name} par mois')\n",
                    "    plt.xlabel('Mois')\n",
                    "    plt.ylabel('Nombre d\\'entrées')\n",
                    "    plt.grid(True)\n",
                    "    plt.tight_layout()\n",
                    "    plt.show()\n",
                    "else:\n",
                    "    print(\"Pas de colonne temporelle pour l'analyse\")"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    f"# Export vers CSV\n",
                    f"export_path = '../../data/exports/{file_name}_data.csv'\n",
                    "df.to_csv(export_path, index=False)\n",
                    f"print(f\"Données exportées vers {export_path}\")"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Création d'un jeu de données synthétique pour les tests\n",
                    "# Générer 10 entrées synthétiques\n",
                    "import random\n",
                    "from datetime import datetime, timedelta\n",
                    "\n",
                    "# Obtenir les champs du modèle\n",
                    "model_fields = [f.name for f in eval(f\"{model_name}._meta.fields\")]\n",
                    "print(f\"Champs du modèle: {model_fields}\")\n",
                    "\n",
                    "# Générer des données de base\n",
                    "synthetic_data = []\n",
                    "for i in range(10):\n",
                    "    data_entry = {}\n",
                    "    # Ajouter des champs communs\n",
                    "    if 'created_at' in model_fields:\n",
                    "        data_entry['created_at'] = (datetime.now() - timedelta(days=i*30)).strftime('%Y-%m-%d %H:%M:%S')\n",
                    "    if 'updated_at' in model_fields:\n",
                    "        data_entry['updated_at'] = (datetime.now() - timedelta(days=i*30)).strftime('%Y-%m-%d %H:%M:%S')\n",
                    "    \n",
                    "    synthetic_data.append(data_entry)\n",
                    "\n",
                    "# Convertir en DataFrame\n",
                    "synthetic_df = pd.DataFrame(synthetic_data)\n",
                    "synthetic_df"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    f"# Exporter les données synthétiques vers CSV pour importation ultérieure\n",
                    f"synthetic_path = '../../data/imports/{file_name}_synthetic_data.csv'\n",
                    "synthetic_df.to_csv(synthetic_path, index=False)\n",
                    f"print(f\"Données synthétiques exportées vers {synthetic_path}\")"
                ]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "codemirror_mode": {
                    "name": "ipython",
                    "version": 3
                },
                "file_extension": ".py",
                "mimetype": "text/x-python",
                "name": "python",
                "nbconvert_exporter": "python",
                "pygments_lexer": "ipython3",
                "version": "3.10.0"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    # Écrire le notebook dans un fichier
    notebook_path = os.path.join(category, f"{file_name}_analysis.ipynb")
    with open(notebook_path, 'w') as f:
        json.dump(notebook, f, indent=2)
    
    print(f"✅ Notebook '{notebook_path}' créé")

# Générer un notebook pour chaque modèle
for category, models in NOTEBOOK_CATEGORIES.items():
    for model in models:
        generate_notebook(model, category)

print("\n✅ Génération des notebooks terminée")
print(f"📊 {sum(len(models) for models in NOTEBOOK_CATEGORIES.values())} notebooks ont été générés")
print("\nPour lancer Jupyter Notebook, exécutez:")
print("jupyter notebook")