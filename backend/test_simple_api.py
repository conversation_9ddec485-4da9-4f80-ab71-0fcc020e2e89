#!/usr/bin/env python3
"""
Test simple de l'API sans authentification
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_endpoints_without_auth():
    """Test des endpoints sans authentification pour voir les erreurs"""
    print("🧪 TEST SIMPLE DES ENDPOINTS")
    print("=" * 50)
    
    endpoints = [
        "donnees-macroeconomiques/",
        "donnees-inflation/",
        "taux-pret-immobilier/",
        "prix-metre-carre/",
        "materiaux-construction/",
        "projections-demographiques/",
        "migrations-internes/"
    ]
    
    for endpoint in endpoints:
        try:
            url = f"{BASE_URL}/{endpoint}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 401:
                print(f"✅ {endpoint:30} - Authentification requise (normal)")
            elif response.status_code == 200:
                data = response.json()
                count = len(data.get('results', data)) if isinstance(data, dict) else len(data)
                print(f"✅ {endpoint:30} - {count} entrées trouvées")
            else:
                print(f"❌ {endpoint:30} - Status: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {endpoint:30} - Erreur: {str(e)[:50]}")
    
    # Test de l'endpoint de documentation
    try:
        response = requests.get("http://127.0.0.1:8000/doc/", timeout=5)
        if response.status_code == 200:
            print(f"✅ {'doc/':30} - Documentation accessible")
        else:
            print(f"❌ {'doc/':30} - Status: {response.status_code}")
    except Exception as e:
        print(f"❌ {'doc/':30} - Erreur: {str(e)[:50]}")

def test_server_status():
    """Test si le serveur répond"""
    try:
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        print(f"🌐 Serveur Django: Status {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ Serveur Django non accessible: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TEST SIMPLE DE L'API KAYDAN ANALYTICS HUB")
    print("=" * 60)
    
    if test_server_status():
        test_endpoints_without_auth()
        
        print(f"\n📖 INSTRUCTIONS:")
        print("1. Ouvrez http://127.0.0.1:8000/doc/ dans votre navigateur")
        print("2. Testez l'authentification avec POST /api/auth/login/")
        print("3. Utilisez le token pour tester les autres endpoints")
    else:
        print("💡 Démarrez le serveur avec: python manage.py runserver")
