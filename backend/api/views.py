from django.shortcuts import render
from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django.shortcuts import get_object_or_404
from knox.auth import TokenAuthentication
from django.db import models
from .models import UserProfile
from .serializers import UserProfileSerializer, UserProfileUpdateSerializer
from accounts.models import CustomUser
from rest_framework.viewsets import ViewSet
import requests
from datetime import datetime

from .models import (
    DQEData,
    GStockApprovisionnement,
    GStockSortie,
    GStockConsommation,
    GStockAchat,
    GProjet,
    EcoleTalents,
    DonneesProduitInterieurBrut,
    donneesInflation,
    DonnesTauxPretImmobilier,
    DonneesPrixMetreCarre,
    DonneesMateriauxConstruction,
    DonneesProjectionDemographique,
    DonneesMigrationInterne,
)
from .serializers import (
    DQEDataSerializer,
    GStockApprovisionnementSerializer,
    GStockSortieSerializer,
    GStockConsommationSerializer,
    GStockAchatSerializer,
    GProjetSerializer,
    EcoleTalentSerializer,
    DonneesProduitInterieurBrutSerializer,
    DonneesInflationSerializer,
    DonnesTauxPretImmobilierSerializer,
    DonneesPrixMetreCarreSerializer,
    DonneesMateriauxConstructionSerializer,
    DonneesProjectionDemographiqueSerializer,
    DonneesMigrationInterneSerializer,
)

# Create your views here.


class UserProfileViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing user profiles
    """

    authentication_classes = (TokenAuthentication,)
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserProfileSerializer

    def get_queryset(self):
        # Handle the case when this is called during schema generation
        if getattr(self, "swagger_fake_view", False):
            return UserProfile.objects.none()
        return UserProfile.objects.filter(user=self.request.user)

    def get_serializer_class(self):
        if self.action in ["update", "partial_update", "update_me"]:
            return UserProfileUpdateSerializer
        return UserProfileSerializer

    def perform_create(self, serializer):
        # Ensure the profile is linked to the current user
        serializer.save(user=self.request.user)

    @action(detail=False, methods=["get"])
    def me(self, request):
        """
        Get the current user's profile
        """
        # Get or create the profile
        profile, created = UserProfile.objects.get_or_create(user=request.user)

        # Get user details including custom user fields
        user_data = {
            "first_name": request.user.first_name,
            "last_name": request.user.last_name,
            "email": request.user.email,
            "kaydan_subsidiary": request.user.kaydan_subsidiary,
        }

        # Get profile data
        profile_data = UserProfileSerializer(profile, context={"request": request}).data

        # Combine both datasets
        combined_data = {**user_data, **profile_data}

        return Response(combined_data)

    @action(detail=False, methods=["put", "patch"])
    def update_me(self, request):
        """
        Update the current user's profile
        """
        profile, created = UserProfile.objects.get_or_create(user=request.user)

        # Extract profile fields from the request
        profile_data = {}
        for field in ["profile_picture", "country", "address"]:
            if field in request.data:
                profile_data[field] = request.data.get(field)

        # Update profile
        profile_serializer = UserProfileUpdateSerializer(
            profile, data=profile_data, partial=True
        )

        if profile_serializer.is_valid():
            try:
                profile_serializer.save()

                # Extract user fields from the request
                user_data = {}
                for field in ["first_name", "last_name", "email", "kaydan_subsidiary"]:
                    if field in request.data:
                        user_data[field] = request.data.get(field)

                # Update user if there are user fields
                if user_data:
                    user = request.user
                    for key, value in user_data.items():
                        setattr(user, key, value)
                    user.save()

                # Return the updated profile with user data
                return self.me(request)
            except ValueError as e:
                return Response(
                    {"subscription_methods": str(e)}, status=status.HTTP_400_BAD_REQUEST
                )

        return Response(profile_serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# ========================================================
# DQE Views
# ========================================================


class KreDQEData(ViewSet):
    """
    ViewSet to fetch and store DQE data from external API
    """

    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """
        Fetch data from external API and save it to the database
        """
        try:
            # URL of the external API
            url = "http://*************:8000/api/dqes/getData-to-kah"

            # Make the request to the external API
            response = requests.get(url, timeout=30)

            # Check if the request was successful
            if response.status_code == 200:
                # Parse the response data
                data = response.json()

                # Create a new DQEData object
                dqe_data = DQEData(
                    title=f"DQE Data - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    description="Data fetched from external DQE API",
                    data=data,
                )

                # Save the data to the database
                dqe_data.save()

                # Serialize the data for the response
                serializer = DQEDataSerializer(dqe_data)

                return Response(
                    {
                        "status": "success",
                        "message": "DQE data fetched and saved successfully",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": f"Failed to fetch data from external API. Status code: {response.status_code}",
                        "response": response.text,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except requests.exceptions.RequestException as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Error connecting to external API: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def get_latest(self, request):
        """
        Get the latest DQE data from the database
        """
        try:
            # Get the latest DQE data
            latest_data = DQEData.objects.order_by("-created_at").first()

            if latest_data:
                # Serialize the data for the response
                serializer = DQEDataSerializer(latest_data)

                return Response(
                    {
                        "status": "success",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"status": "error", "message": "No DQE data found in the database"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def list_all(self, request):
        """
        List all DQE data entries
        """
        try:
            # Get all DQE data entries, ordered by creation date (newest first)
            all_data = DQEData.objects.all().order_by("-created_at")

            # Serialize the data for the response
            serializer = DQEDataSerializer(all_data, many=True)

            return Response(
                {
                    "status": "success",
                    "count": len(all_data),
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# ========================================================
# G-Stock Views
# ========================================================


class GStockApprovisionnementViewSet(viewsets.ModelViewSet):
    """
    ViewSet to manage G-Stock Approvisionnement data
    """

    queryset = GStockApprovisionnement.objects.all()
    serializer_class = GStockApprovisionnementSerializer
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """
        Fetch data from external API and save it to the database
        """
        try:
            # URL of the external API
            url = "https://gstock.artemisconstruction-ci.com/params/get-appro"

            # Make the request to the external API with SSL verification disabled
            response = requests.get(url, timeout=30, verify=False)

            if response.status_code == 200:
                # Parse the response data
                data = response.json()

                # Create a new GStockApprovisionnement object
                gstock_approvisionnement = GStockApprovisionnement(
                    title=f"G-Stock Approvisionnement - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    description="Data fetched from external G-Stock API",
                    data=data,
                )

                # Save the data to the database
                gstock_approvisionnement.save()

                # Serialize the data for the response
                serializer = GStockApprovisionnementSerializer(gstock_approvisionnement)

                return Response(
                    {
                        "status": "success",
                        "message": "G-Stock Approvisionnement data fetched and saved successfully",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": f"Failed to fetch data from external API. Status code: {response.status_code}",
                        "response": response.text,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except requests.exceptions.RequestException as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Error connecting to external API: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def get_latest(self, request):
        """
        Get the latest G-Stock Approvisionnement data from the database
        """
        try:
            # Get the latest G-Stock Approvisionnement data
            latest_data = GStockApprovisionnement.objects.order_by(
                "-created_at"
            ).first()

            if latest_data:
                # Serialize the data for the response
                serializer = GStockApprovisionnementSerializer(latest_data)

                return Response(
                    {
                        "status": "success",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": "No G-Stock Approvisionnement data found in the database",
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def list_all(self, request):
        """
        List all G-Stock Approvisionnement data entries
        """
        try:
            # Get all G-Stock Approvisionnement data entries, ordered by creation date (newest first)
            all_data = GStockApprovisionnement.objects.all().order_by("-created_at")

            # Serialize the data for the response
            serializer = GStockApprovisionnementSerializer(all_data, many=True)

            return Response(
                {
                    "status": "success",
                    "count": len(all_data),
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# ========================================================
# G-Stock Sortie Views
# ========================================================


class GStockSortieViewSet(viewsets.ModelViewSet):
    """
    ViewSet to manage G-Stock Sortie data
    """

    queryset = GStockSortie.objects.all()
    serializer_class = GStockSortieSerializer
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """
        Fetch data from external API and save it to the database
        """
        try:
            # URL of the external API
            url = "https://gstock.artemisconstruction-ci.com/params/get-sortie-stock"

            # Make the request to the external API with SSL verification disabled
            response = requests.get(url, timeout=30, verify=False)

            if response.status_code == 200:
                # Parse the response data
                data = response.json()

                # Create a new GStockSortie object
                gstock_sortie = GStockSortie(
                    title=f"G-Stock Sortie - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    description="Data fetched from external G-Stock API",
                    data=data,
                )

                # Save the data to the database
                gstock_sortie.save()

                # Serialize the data for the response
                serializer = GStockSortieSerializer(gstock_sortie)

                return Response(
                    {
                        "status": "success",
                        "message": "G-Stock Sortie data fetched and saved successfully",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                error_message = "Failed to fetch data from external API"
                if response.status_code == 404:
                    error_message = "The requested endpoint does not exist. Please check the API URL."
                elif response.status_code == 401:
                    error_message = (
                        "Authentication failed. Please check your credentials."
                    )
                elif response.status_code == 403:
                    error_message = "Access forbidden. Please check your permissions."

                return Response(
                    {
                        "status": "error",
                        "message": f"{error_message}. Status code: {response.status_code}",
                        "response": response.text,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except requests.exceptions.RequestException as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Error connecting to external API: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def get_latest(self, request):
        """
        Get the latest G-Stock Sortie data from the database
        """
        try:
            # Get the latest G-Stock Sortie data
            latest_data = GStockSortie.objects.order_by("-created_at").first()

            if latest_data:
                # Serialize the data for the response
                serializer = GStockSortieSerializer(latest_data)

                return Response(
                    {
                        "status": "success",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": "No G-Stock Sortie data found in the database",
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def list_all(self, request):
        """
        List all G-Stock Sortie data entries
        """
        try:
            # Get all G-Stock Sortie data entries, ordered by creation date (newest first)
            all_data = GStockSortie.objects.all().order_by("-created_at")

            # Serialize the data for the response
            serializer = GStockSortieSerializer(all_data, many=True)

            return Response(
                {
                    "status": "success",
                    "count": len(all_data),
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# ========================================================
# G-Stock Consommation View Set
# ========================================================


class GStockConsommationViewSet(viewsets.ModelViewSet):
    """
    ViewSet to manage G-Stock Consommation data
    """

    queryset = GStockConsommation.objects.all()
    serializer_class = GStockConsommationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """
        Fetch data from external API and save it to the database
        """
        try:
            # URL of the external API
            url = "https://gstock.artemisconstruction-ci.com/params/get-niv-conso-stock"

            # Make the request to the external API with SSL verification disabled
            response = requests.get(url, timeout=30, verify=False)

            if response.status_code == 200:
                # Parse the response data
                data = response.json()

                # Create a new GStockConsommation object
                gstock_consommation = GStockConsommation(
                    title=f"G-Stock Consommation - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    description="Data fetched from external G-Stock API",
                    data=data,
                )

                # Save the data to the database
                gstock_consommation.save()

                # Serialize the data for the response
                serializer = GStockConsommationSerializer(gstock_consommation)

                return Response(
                    {
                        "status": "success",
                        "message": "G-Stock Consommation data fetched and saved successfully",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": f"Failed to fetch data from external API. Status code: {response.status_code}",
                        "response": response.text,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except requests.exceptions.RequestException as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Error connecting to external API: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def get_latest(self, request):
        """
        Get the latest G-Stock Consommation data from the database
        """
        try:
            # Get the latest G-Stock Consommation data
            latest_data = GStockConsommation.objects.order_by("-created_at").first()

            if latest_data:
                # Serialize the data for the response
                serializer = GStockConsommationSerializer(latest_data)

                return Response(
                    {
                        "status": "success",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": "No G-Stock Consommation data found in the database",
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def list_all(self, request):
        """
        List all G-Stock Consommation data entries
        """
        try:
            # Get all G-Stock Consommation data entries, ordered by creation date (newest first)
            all_data = GStockConsommation.objects.all().order_by("-created_at")

            # Serialize the data for the response
            serializer = GStockConsommationSerializer(all_data, many=True)

            return Response(
                {
                    "status": "success",
                    "count": len(all_data),
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# ========================================================
# G-Stock Achat View Set
# ========================================================


class GStockAchatViewSet(viewsets.ModelViewSet):
    """
    ViewSet to manage G-Stock Achat data
    """

    queryset = GStockAchat.objects.all()
    serializer_class = GStockAchatSerializer
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """
        Fetch data from external API and save it to the database
        """
        try:
            # URL of the external API
            url = "https://gstock.artemisconstruction-ci.com/params/get-achat-materiaux"

            # Make the request to the external API with SSL verification disabled
            response = requests.get(url, timeout=30, verify=False)

            if response.status_code == 200:
                # Parse the response data
                data = response.json()

                # Create a new GStockAchat object
                gstock_achat = GStockAchat(
                    title=f"G-Stock Achat - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    description="Data fetched from external G-Stock API",
                    data=data,
                )

                # Save the data to the database
                gstock_achat.save()

                # Serialize the data for the response
                serializer = GStockAchatSerializer(gstock_achat)

                return Response(
                    {
                        "status": "success",
                        "message": "G-Stock Achat data fetched and saved successfully",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": f"Failed to fetch data from external API. Status code: {response.status_code}",
                        "response": response.text,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except requests.exceptions.RequestException as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Error connecting to external API: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def get_latest(self, request):
        """
        Get the latest G-Stock Achat data from the database
        """
        try:
            # Get the latest G-Stock Achat data
            latest_data = GStockAchat.objects.order_by("-created_at").first()

            if latest_data:
                # Serialize the data for the response
                serializer = GStockAchatSerializer(latest_data)

                return Response(
                    {
                        "status": "success",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": "No G-Stock Achat data found in the database",
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def list_all(self, request):
        """
        List all G-Stock Achat data entries
        """
        try:
            # Get all G-Stock Achat data entries, ordered by creation date (newest first)
            all_data = GStockAchat.objects.all().order_by("-created_at")

            # Serialize the data for the response
            serializer = GStockAchatSerializer(all_data, many=True)

            return Response(
                {
                    "status": "success",
                    "count": len(all_data),
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# ========================================================
# G-Projet View set
# ========================================================
class GProjetViewSet(viewsets.ModelViewSet):
    """
    ViewSet to manage G-Projet data
    """

    queryset = GProjet.objects.all()
    serializer_class = GProjetSerializer
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """
        Fetch data from external API and save it to the database
        """
        try:
            # URL of the external API
            url = "http://gprojet.kaydanrealestate.com:9000/params/tasks"

            # Make the request to the external API with SSL verification disabled
            response = requests.get(url, timeout=30, verify=False)

            if response.status_code == 200:
                # Parse the response data
                data = response.json()

                # Create a new GProjet object
                gprojet = GProjet(
                    title=f"G-Projet - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    description="Data fetched from external G-Projet API",
                    data=data,
                )

                # Save the data to the database
                gprojet.save()

                # Serialize the data for the response
                serializer = GProjetSerializer(gprojet)

                return Response(
                    {
                        "status": "success",
                        "message": "G-Projet data fetched and saved successfully",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": f"Failed to fetch data from external API. Status code: {response.status_code}",
                        "response": response.text,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except requests.exceptions.RequestException as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Error connecting to external API: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def get_latest(self, request):
        """
        Get the latest G-Projet data from the database
        """
        try:
            # Get the latest G-Projet  data
            latest_data = GProjet.objects.order_by("-created_at").first()

            if latest_data:
                # Serialize the data for the response
                serializer = GProjetSerializer(latest_data)

                return Response(
                    {
                        "status": "success",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": "No G-Projet data found in the database",
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def list_all(self, request):
        """
        List all G-Projet data entries
        """
        try:
            # Get all G-Stock data entries, ordered by creation date (newest first)
            all_data = GProjet.objects.all().order_by("-created_at")

            # Serialize the data for the response
            serializer = GProjetSerializer(all_data, many=True)

            return Response(
                {
                    "status": "success",
                    "count": len(all_data),
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# ========================================================
# Ecole des talents View set
# ========================================================
class EcoleTalentViewSet(viewsets.ModelViewSet):
    """
    ViewSet to manage Ecole des talent data
    """

    queryset = EcoleTalents.objects.all()
    serializer_class = EcoleTalentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """
        Fetch data from external API and save it to the database
        """
        try:
            # URL of the external API
            url = "https://talents.kaydangroupe.com/params/data-to-kah"

            # Make the request to the external API with SSL verification disabled
            response = requests.get(url, timeout=30, verify=False)

            if response.status_code == 200:
                # Parse the response data
                data = response.json()

                # Create a new Ecole des talents object
                ecoletalents = EcoleTalents(
                    title=f"Ecole des Talents - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    description="Data fetched from external Ecole des Talents API",
                    data=data,
                )

                # Save the data to the database
                ecoletalents.save()

                # Serialize the data for the response
                serializer = EcoleTalentSerializer(ecoletalents)

                return Response(
                    {
                        "status": "success",
                        "message": "Ecole des Talents data fetched and saved successfully",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": f"Failed to fetch data from external API. Status code: {response.status_code}",
                        "response": response.text,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except requests.exceptions.RequestException as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Error connecting to external API: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def get_latest(self, request):
        """
        Get the latest Ecole des Talents data from the database
        """
        try:
            # Get the latest Ecole des Talents data
            latest_data = EcoleTalents.objects.order_by("-created_at").first()

            if latest_data:
                # Serialize the data for the response
                serializer = EcoleTalentSerializer(latest_data)

                return Response(
                    {
                        "status": "success",
                        "data": serializer.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": "No Ecole des Talents data found in the database",
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def list_all(self, request):
        """
        List all ecole des talents data entries
        """
        try:
            # Get all ecole des talents data entries, ordered by creation date (newest first)
            all_data = EcoleTalents.objects.all().order_by("-created_at")

            # Serialize the data for the response
            serializer = EcoleTalentSerializer(all_data, many=True)

            return Response(
                {
                    "status": "success",
                    "count": len(all_data),
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

# ========================================================
# Vue pour les Donnees Produit interieur brut View set
# ========================================================
class DonneesProduitInterieurBrutViewSet(viewsets.ModelViewSet):
    """
    ViewSet to manage Donnees Produit interieur brut data
    """

    queryset = DonneesProduitInterieurBrut.objects.all()
    serializer_class = DonneesProduitInterieurBrutSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=["post"])
    def import_csv(self, request):
        """
        Importer des données macroéconomiques depuis un fichier CSV
        """
        try:
            if 'flie' not in request.FILES:
                return Response(
                    {
                        "status": "error",
                        "message": "Aucun fichier n'a été fourni.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            csv_file = request.FILES['file']
            if not csv_file.name.endswith('.csv'):
                return Response(
                    {
                        "status": "error",
                        "message": "Le fichier doit être au format CSV.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Lire le fichier CSV et importer les données dans la base de données
            return Response(
                {
                    "status": "success",
                    "message": "Données importées avec succès.",
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Une erreur s'est produite: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    @action(detail=False, methods=["get"])
    def filter_by_country(self, request):
        """
        Filtrer les données par pays
        """
        country_code = request.query_params.get("country_code", None)
        if not country_code:
            return Response(
                {
                    "status": "error",
                    "message": "Le code du pays est requis.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        data = self.queryset.filter(country_code=country_code)
        serializer = self.serializer_class(data, many=True)

        return Response(
            {
                "status":"success",
                "count": len(data),
                "data": serializer.data,    
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def filter_by_indicator(self, request):
        """
        Filtrer les données par indicateur
        """
        indicator_code = request.query_params.get("indicator_code", None)
        if not indicator_code:
            return Response(
                {
                    "status": "error",
                    "message": "Le code de l'indicateur est requis.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        data = self.queryset.filter(indicator_code=indicator_code)
        serializer = self.serializer_class(data, many=True)

        return Response(
            {
                "status":"success",
                "count": len(data),
                "data": serializer.data,    
            },
            status=status.HTTP_200_OK,
        )
        
            
class DonneesInflationViewSet(viewsets.ModelViewSet):
    """
    ViewSet to manage Donnees Inflation data
    """

    queryset = donneesInflation.objects.all()
    serializer_class = DonneesInflationSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=["post"])
    def import_csv(self, request):
        """
        Importer des données d'inflation depuis un fichier CSV
        """
        try:
            if 'file' not in request.FILES:
                return Response(
                    {
                        "status": "error",
                        "message": "Aucun fichier n'a été fourni.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            csv_file = request.FILES['file']
            if not csv_file.name.endswith('.csv'):
                return Response(
                    {
                        "status": "error",
                        "message": "Le fichier doit être au format CSV.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Lire le fichier CSV et importer les données dans la base de données
            
                    
            return Response(
                {
                    "status":"success",
                    "message":"Donnees d'inflation charger avec success"
                },
                status=status.HTTP_200_OK
            )
        
        except Exception as e:
            return Response(
                {
                    "status":"error",
                    "message":f"Une erreur s'est produite: {str(e)}"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    @action(detail=False, methods=["get"])
    def filter_by_country(self, request):
        """
        Filtrer les données par pays
        """
        country_code = request.query_params.get("country_code", None)
        if not country_code:
            return Response(
                {
                    "status": "error",
                    "message": "Le code du pays est requis.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        data = self.queryset.filter(country_code=country_code)
        serializer = self.serializer_class(data, many=True)

        return Response(
            {
                "status":"success",
                "count": len(data),
                "data": serializer.data,    
            },
            status=status.HTTP_200_OK,
        )

class DonnesTauxPretImmobilierViewSet(viewsets.ModelViewSet):
    """
    Viewset to manage Donnes Taux Pret Immobilier data    
    """
    queryset = DonnesTauxPretImmobilier.objects.all()
    serializer_class = DonnesTauxPretImmobilierSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=["post"])
    def import_csv(self, request):
        """
        Importer des données de taux de pret immobilier depuis un fichier CSV
        """
        try:
            if 'file' not in request.FILES:
                return Response(
                    {
                        "status": "error",
                        "message": "Aucun fichier n'a été fourni.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            csv_file = request.FILES['file']
            if not csv_file.name.endswith('.csv'):
                return Response(
                    {
                        "status": "error",
                        "message": "Le fichier doit être au format CSV.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            # Traitement du fichier CSV

            return Response(
                {
                    "status":"success",
                    "message":"Donnees de taux de pret immobilier charger avec success"
                },
                status=status.HTTP_200_OK
            )
        
        except Exception as e:
            return Response(
                {
                    "status":"error",
                    "message":f"Une erreur s'est produite: {str(e)}"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=["get"])
    def filter_by_banque(self, request):
        """
        Filtrer les données par banque
        """
        banque = request.query_params.get("banque", None)
        if not banque:
            return Response(
                {
                    "status": "error",
                    "message": "Le nom de la banque est requis.",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        data = self.queryset.filter(banque=banque)
        serializer = self.serializer_class(data, many=True)

        return Response(
            {
                "status":"success",
                "count":len(data),
                "data":serializer.data
            },
            status=status.HTTP_200_OK
        )


# ========================================================
# Vues pour les données du marché immobilier
# ========================================================

class DonneesPrixMetreCarreViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour gérer les données de prix au mètre carré
    """
    queryset = DonneesPrixMetreCarre.objects.all()
    serializer_class = DonneesPrixMetreCarreSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['post'])
    def import_csv(self, request):
        """
        Importer des données de prix au mètre carré depuis un fichier CSV
        """
        try:
            if 'file' not in request.FILES:
                return Response(
                    {"status": "error", "message": "Aucun fichier n'a été fourni"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            csv_file = request.FILES['file']
            if not csv_file.name.endswith('.csv'):
                return Response(
                    {"status": "error", "message": "Le fichier doit être au format CSV"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Traitement du fichier CSV
            # Code pour lire et importer les données du CSV
            
            return Response(
                {
                    "status": "success",
                    "message": "Données de prix au mètre carré importées avec succès"
                },
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Une erreur s'est produite lors de l'importation: {str(e)}"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def filter_by_commune(self, request):
        """
        Filtrer les données par commune
        """
        commune = request.query_params.get('commune', None)
        if not commune:
            return Response(
                {"status": "error", "message": "Le paramètre commune est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        data = self.queryset.filter(commune=commune)
        serializer = self.serializer_class(data, many=True)
        
        return Response(
            {
                "status": "success",
                "count": len(data),
                "data": serializer.data
            },
            status=status.HTTP_200_OK
        )

class DonneesMateriauxConstructionViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour gérer les données de matériaux de construction
    """
    queryset = DonneesMateriauxConstruction.objects.all()
    serializer_class = DonneesMateriauxConstructionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['post'])
    def import_csv(self, request):
        """
        Importer des données de matériaux de construction depuis un fichier CSV
        """
        try:
            if 'file' not in request.FILES:
                return Response(
                    {"status": "error", "message": "Aucun fichier n'a été fourni"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            csv_file = request.FILES['file']
            if not csv_file.name.endswith('.csv'):
                return Response(
                    {"status": "error", "message": "Le fichier doit être au format CSV"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Traitement du fichier CSV
            # Code pour lire et importer les données du CSV
            
            return Response(
                {
                    "status": "success",
                    "message": "Données de matériaux de construction importées avec succès"
                },
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Une erreur s'est produite lors de l'importation: {str(e)}"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def filter_by_categorie(self, request):
        """
        Filtrer les données par catégorie
        """
        categorie = request.query_params.get('categorie', None)
        if not categorie:
            return Response(
                {"status": "error", "message": "Le paramètre categorie est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        data = self.queryset.filter(categorie=categorie)
        serializer = self.serializer_class(data, many=True)
        
        return Response(
            {
                "status": "success",
                "count": len(data),
                "data": serializer.data
            },
            status=status.HTTP_200_OK
        )


# ========================================================
# Vues pour les données démographiques et sociales
# ========================================================

class DonneesProjectionDemographiqueViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour gérer les données de projections démographiques
    """
    queryset = DonneesProjectionDemographique.objects.all()
    serializer_class = DonneesProjectionDemographiqueSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['post'])
    def import_csv(self, request):
        """
        Importer des données de projections démographiques depuis un fichier CSV
        """
        try:
            if 'file' not in request.FILES:
                return Response(
                    {"status": "error", "message": "Aucun fichier n'a été fourni"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            csv_file = request.FILES['file']
            if not csv_file.name.endswith('.csv'):
                return Response(
                    {"status": "error", "message": "Le fichier doit être au format CSV"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Traitement du fichier CSV
            # Code pour lire et importer les données du CSV
            
            return Response(
                {
                    "status": "success",
                    "message": "Données de projections démographiques importées avec succès"
                },
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Une erreur s'est produite lors de l'importation: {str(e)}"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def filter_by_region(self, request):
        """
        Filtrer les données par région
        """
        region = request.query_params.get('region', None)
        if not region:
            return Response(
                {"status": "error", "message": "Le paramètre region est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        data = self.queryset.filter(region=region)
        serializer = self.serializer_class(data, many=True)
        
        return Response(
            {
                "status": "success",
                "count": len(data),
                "data": serializer.data
            },
            status=status.HTTP_200_OK
        )


class DonneesMigrationInterneViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour gérer les données de migration interne
    """
    queryset = DonneesMigrationInterne.objects.all()
    serializer_class = DonneesMigrationInterneSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['post'])
    def import_csv(self, request):
        """
        Importer des données de migration interne depuis un fichier CSV
        """
        try:
            if 'file' not in request.FILES:
                return Response(
                    {"status": "error", "message": "Aucun fichier n'a été fourni"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            csv_file = request.FILES['file']
            if not csv_file.name.endswith('.csv'):
                return Response(
                    {"status": "error", "message": "Le fichier doit être au format CSV"},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Traitement du fichier CSV
            # Code pour lire et importer les données du CSV
            
            return Response(
                {
                    "status": "success",
                    "message": "Données de migration interne importées avec succès"
                },
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Une erreur s'est produite lors de l'importation: {str(e)}"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def filter_by_region_origine(self, request):
        """
        Filtrer les données par région d'origine
        """
        region = request.query_params.get('region', None)
        if not region:
            return Response(
                {"status": "error", "message": "Le paramètre region est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        data = self.queryset.filter(region_origine=region)
        serializer = self.serializer_class(data, many=True)
        
        return Response(
            {
                "status": "success",
                "count": len(data),
                "data": serializer.data
            },
            status=status.HTTP_200_OK
        )
    
    @action(detail=False, methods=['get'])
    def filter_by_region_destination(self, request):
        """
        Filtrer les données par région de destination
        """
        region = request.query_params.get('region', None)
        if not region:
            return Response(
                {"status": "error", "message": "Le paramètre region est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        data = self.queryset.filter(region_destination=region)
        serializer = self.serializer_class(data, many=True)
        
        return Response(
            {
                "status": "success",
                "count": len(data),
                "data": serializer.data
            },
            status=status.HTTP_200_OK
        )


# ========================================================
# ViewSets pour les nouveaux modèles de données externes
# ========================================================

class DonneesProduitInterieurBrutViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données PIB (Produit Intérieur Brut)
    Fournit les opérations CRUD complètes : GET, POST, PUT, PATCH, DELETE
    """

    queryset = DonneesProduitInterieurBrut.objects.all()
    serializer_class = DonneesProduitInterieurBrutSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filtrage optionnel par paramètres de requête"""
        queryset = DonneesProduitInterieurBrut.objects.all()

        # Filtrage par année
        annee = self.request.query_params.get('annee', None)
        if annee:
            queryset = queryset.filter(annee_deb_couv=annee)

        # Filtrage par plage de valeurs
        valeur_min = self.request.query_params.get('valeur_min', None)
        valeur_max = self.request.query_params.get('valeur_max', None)
        if valeur_min:
            queryset = queryset.filter(valeur__gte=valeur_min)
        if valeur_max:
            queryset = queryset.filter(valeur__lte=valeur_max)

        return queryset.order_by('-annee_deb_couv', '-mois_deb_couv')

    @action(detail=False, methods=['get'])
    def latest(self, request):
        """Récupère les données PIB les plus récentes"""
        latest_data = self.get_queryset().first()
        if latest_data:
            serializer = self.get_serializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        return Response({
            "status": "error",
            "message": "Aucune donnée PIB trouvée"
        }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def by_year(self, request):
        """Récupère les données PIB par année"""
        annee = request.query_params.get('annee')
        if not annee:
            return Response({
                "status": "error",
                "message": "Paramètre 'annee' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        data = self.get_queryset().filter(annee_deb_couv=annee)
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })


class DonneesInflationViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données d'inflation
    Fournit les opérations CRUD complètes : GET, POST, PUT, PATCH, DELETE
    """

    queryset = donneesInflation.objects.all()
    serializer_class = DonneesInflationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filtrage optionnel par paramètres de requête"""
        queryset = donneesInflation.objects.all()

        # Filtrage par pays
        country_code = self.request.query_params.get('country_code', None)
        if country_code:
            queryset = queryset.filter(country_code=country_code)

        # Filtrage par année
        year = self.request.query_params.get('year', None)
        if year:
            queryset = queryset.filter(year=year)

        # Filtrage par plage d'années
        year_start = self.request.query_params.get('year_start', None)
        year_end = self.request.query_params.get('year_end', None)
        if year_start:
            queryset = queryset.filter(year__gte=year_start)
        if year_end:
            queryset = queryset.filter(year__lte=year_end)

        return queryset.order_by('-year')

    @action(detail=False, methods=['get'])
    def by_country(self, request):
        """Récupère les données d'inflation par pays"""
        country_code = request.query_params.get('country_code')
        if not country_code:
            return Response({
                "status": "error",
                "message": "Paramètre 'country_code' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        data = self.get_queryset().filter(country_code=country_code)
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })

    @action(detail=False, methods=['get'])
    def latest_by_country(self, request):
        """Récupère les données d'inflation les plus récentes par pays"""
        country_code = request.query_params.get('country_code')
        if not country_code:
            return Response({
                "status": "error",
                "message": "Paramètre 'country_code' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        latest_data = self.get_queryset().filter(country_code=country_code).first()
        if latest_data:
            serializer = self.get_serializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        return Response({
            "status": "error",
            "message": f"Aucune donnée d'inflation trouvée pour le pays {country_code}"
        }, status=status.HTTP_404_NOT_FOUND)


class DonnesTauxPretImmobilierViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données de taux de prêt immobilier
    Fournit les opérations CRUD complètes : GET, POST, PUT, PATCH, DELETE
    """

    queryset = DonnesTauxPretImmobilier.objects.all()
    serializer_class = DonnesTauxPretImmobilierSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filtrage optionnel par paramètres de requête"""
        queryset = DonnesTauxPretImmobilier.objects.all()

        # Filtrage par banque
        banque = self.request.query_params.get('banque', None)
        if banque:
            queryset = queryset.filter(banque__icontains=banque)

        # Filtrage par type de prêt
        type_pret = self.request.query_params.get('type_pret', None)
        if type_pret:
            queryset = queryset.filter(type_pret__icontains=type_pret)

        return queryset.order_by('banque', 'type_pret')

    @action(detail=False, methods=['get'])
    def by_bank(self, request):
        """Récupère les taux par banque"""
        banque = request.query_params.get('banque')
        if not banque:
            return Response({
                "status": "error",
                "message": "Paramètre 'banque' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        data = self.get_queryset().filter(banque__icontains=banque)
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })

    @action(detail=False, methods=['get'])
    def by_loan_type(self, request):
        """Récupère les taux par type de prêt"""
        type_pret = request.query_params.get('type_pret')
        if not type_pret:
            return Response({
                "status": "error",
                "message": "Paramètre 'type_pret' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        data = self.get_queryset().filter(type_pret__icontains=type_pret)
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })


class DonneesPrixMetreCarreViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données de prix au mètre carré
    Fournit les opérations CRUD complètes : GET, POST, PUT, PATCH, DELETE
    """

    queryset = DonneesPrixMetreCarre.objects.all()
    serializer_class = DonneesPrixMetreCarreSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filtrage optionnel par paramètres de requête"""
        queryset = DonneesPrixMetreCarre.objects.all()

        # Filtrage par commune
        commune = self.request.query_params.get('commune', None)
        if commune:
            queryset = queryset.filter(commune__icontains=commune)

        # Filtrage par plage de prix
        prix_min = self.request.query_params.get('prix_min', None)
        prix_max = self.request.query_params.get('prix_max', None)
        if prix_min:
            queryset = queryset.filter(prix_moyen__gte=prix_min)
        if prix_max:
            queryset = queryset.filter(prix_moyen__lte=prix_max)

        return queryset.order_by('-prix_moyen')

    @action(detail=False, methods=['get'])
    def by_commune(self, request):
        """Récupère les prix par commune"""
        commune = request.query_params.get('commune')
        if not commune:
            return Response({
                "status": "error",
                "message": "Paramètre 'commune' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        data = self.get_queryset().filter(commune__icontains=commune)
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })

    @action(detail=False, methods=['get'])
    def price_range(self, request):
        """Récupère les prix dans une plage donnée"""
        prix_min = request.query_params.get('prix_min')
        prix_max = request.query_params.get('prix_max')

        if not prix_min or not prix_max:
            return Response({
                "status": "error",
                "message": "Paramètres 'prix_min' et 'prix_max' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        data = self.get_queryset().filter(
            prix_moyen__gte=prix_min,
            prix_moyen__lte=prix_max
        )
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })


class DonneesMateriauxConstructionViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données de matériaux de construction
    Fournit les opérations CRUD complètes : GET, POST, PUT, PATCH, DELETE
    """

    queryset = DonneesMateriauxConstruction.objects.all()
    serializer_class = DonneesMateriauxConstructionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filtrage optionnel par paramètres de requête"""
        queryset = DonneesMateriauxConstruction.objects.all()

        # Filtrage par catégorie
        categorie = self.request.query_params.get('categorie', None)
        if categorie:
            queryset = queryset.filter(categorie__icontains=categorie)

        # Recherche dans le titre
        titre = self.request.query_params.get('titre', None)
        if titre:
            queryset = queryset.filter(titre__icontains=titre)

        return queryset.order_by('categorie', 'titre')

    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """Récupère les matériaux par catégorie"""
        categorie = request.query_params.get('categorie')
        if not categorie:
            return Response({
                "status": "error",
                "message": "Paramètre 'categorie' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        data = self.get_queryset().filter(categorie__icontains=categorie)
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Recherche dans les matériaux"""
        query = request.query_params.get('q')
        if not query:
            return Response({
                "status": "error",
                "message": "Paramètre 'q' requis pour la recherche"
            }, status=status.HTTP_400_BAD_REQUEST)

        data = self.get_queryset().filter(
            models.Q(titre__icontains=query) |
            models.Q(description__icontains=query) |
            models.Q(categorie__icontains=query)
        )
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })


class DonneesProjectionDemographiqueViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données de projection démographique
    Fournit les opérations CRUD complètes : GET, POST, PUT, PATCH, DELETE
    """

    queryset = DonneesProjectionDemographique.objects.all()
    serializer_class = DonneesProjectionDemographiqueSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filtrage optionnel par paramètres de requête"""
        queryset = DonneesProjectionDemographique.objects.all()

        # Filtrage par région
        region = self.request.query_params.get('region', None)
        if region:
            queryset = queryset.filter(region__icontains=region)

        # Filtrage par année
        annee = self.request.query_params.get('annee', None)
        if annee:
            queryset = queryset.filter(annee=annee)

        # Filtrage par plage d'années
        annee_start = self.request.query_params.get('annee_start', None)
        annee_end = self.request.query_params.get('annee_end', None)
        if annee_start:
            queryset = queryset.filter(annee__gte=annee_start)
        if annee_end:
            queryset = queryset.filter(annee__lte=annee_end)

        return queryset.order_by('-annee', 'region')

    @action(detail=False, methods=['get'])
    def by_region(self, request):
        """Récupère les projections par région"""
        region = request.query_params.get('region')
        if not region:
            return Response({
                "status": "error",
                "message": "Paramètre 'region' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        data = self.get_queryset().filter(region__icontains=region)
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })

    @action(detail=False, methods=['get'])
    def latest_by_region(self, request):
        """Récupère la projection la plus récente par région"""
        region = request.query_params.get('region')
        if not region:
            return Response({
                "status": "error",
                "message": "Paramètre 'region' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        latest_data = self.get_queryset().filter(region__icontains=region).first()
        if latest_data:
            serializer = self.get_serializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        return Response({
            "status": "error",
            "message": f"Aucune projection trouvée pour la région {region}"
        }, status=status.HTTP_404_NOT_FOUND)


class DonneesMigrationInterneViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données de migration interne
    Fournit les opérations CRUD complètes : GET, POST, PUT, PATCH, DELETE
    """

    queryset = DonneesMigrationInterne.objects.all()
    serializer_class = DonneesMigrationInterneSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filtrage optionnel par paramètres de requête"""
        queryset = DonneesMigrationInterne.objects.all()

        # Filtrage par région d'origine
        region_origine = self.request.query_params.get('region_origine', None)
        if region_origine:
            queryset = queryset.filter(region_origine__icontains=region_origine)

        # Filtrage par région de destination
        region_destination = self.request.query_params.get('region_destination', None)
        if region_destination:
            queryset = queryset.filter(region_destination__icontains=region_destination)

        # Filtrage par année
        annee = self.request.query_params.get('annee_reference', None)
        if annee:
            queryset = queryset.filter(annee_reference=annee)

        return queryset.order_by('-annee_reference', '-nombre_migrants_total')

    @action(detail=False, methods=['get'])
    def by_origin(self, request):
        """Récupère les migrations par région d'origine"""
        region_origine = request.query_params.get('region_origine')
        if not region_origine:
            return Response({
                "status": "error",
                "message": "Paramètre 'region_origine' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        data = self.get_queryset().filter(region_origine__icontains=region_origine)
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })

    @action(detail=False, methods=['get'])
    def by_destination(self, request):
        """Récupère les migrations par région de destination"""
        region_destination = request.query_params.get('region_destination')
        if not region_destination:
            return Response({
                "status": "error",
                "message": "Paramètre 'region_destination' requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        data = self.get_queryset().filter(region_destination__icontains=region_destination)
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })

    @action(detail=False, methods=['get'])
    def top_migrations(self, request):
        """Récupère les plus importantes migrations"""
        limit = request.query_params.get('limit', 10)
        try:
            limit = int(limit)
        except ValueError:
            limit = 10

        data = self.get_queryset()[:limit]
        serializer = self.get_serializer(data, many=True)

        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })

    @action(detail=False, methods=['get'])
    def migration_flows(self, request):
        """Analyse des flux migratoires"""
        # Agrégation par région de destination
        from django.db.models import Sum

        flows = self.get_queryset().values('region_destination').annotate(
            total_migrants=Sum('nombre_migrants_total')
        ).order_by('-total_migrants')

        return Response({
            "status": "success",
            "count": len(flows),
            "data": list(flows)
        })