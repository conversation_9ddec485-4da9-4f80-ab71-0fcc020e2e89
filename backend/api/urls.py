from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    UserProfileViewSet,
    KreDQEData,
    GStockApprovisionnementViewSet,
    GStockSortieViewSet,
    GStockConsommationViewSet,
    GStockAchatViewSet,
    GProjetViewSet,
    EcoleTalentViewSet,
    # Nouveaux ViewSets
    DonneesProduitInterieurBrutViewSet,
    DonneesInflationViewSet,
    DonnesTauxPretImmobilierViewSet,
    DonneesPrixMetreCarreViewSet,
    DonneesMateriauxConstructionViewSet,
    DonneesProjectionDemographiqueViewSet,
    DonneesMigrationInterneViewSet,
)

router = DefaultRouter()
router.register(r"profiles", UserProfileViewSet, basename="profile")
router.register(r"dqe", KreDQEData, basename="dqe-data")
router.register(
    r"gstock-approvisionnement",
    GStockApprovisionnementViewSet,
    basename="gstock-approvisionnement",
)
router.register(r"gstock-sortie", GStockSortieViewSet, basename="gstock-sortie")
router.register(
    r"gstock-consommation", GStockConsommationViewSet, basename="gstock-consommation"
)
router.register(r"gstock-achat", GStockAchatViewSet, basename="gstock-achat")
router.register(r"gprojet", GProjetViewSet, basename="gprojet")
router.register(r"ecole-talent", EcoleTalentViewSet, basename="ecole-talent")
# Enregistrement des nouveaux ViewSets
# Données macroéconomiques
router.register(
    r"donnees-macroeconomiques",
    DonneesProduitInterieurBrutViewSet,
    basename="donnees-macroeconomiques"
)
router.register(
    r"donnees-inflation",
    DonneesInflationViewSet,
    basename="donnees-inflation"
)
router.register(
    r"taux-pret-immobilier",
    DonnesTauxPretImmobilierViewSet,
    basename="taux-pret-immobilier"
)

# Données du marché immobilier
router.register(
    r"prix-metre-carre",
    DonneesPrixMetreCarreViewSet,
    basename="prix-metre-carre"
)
router.register(
    r"materiaux-construction",
    DonneesMateriauxConstructionViewSet,
    basename="materiaux-construction"
)

# Données démographiques et sociales
router.register(
    r"projections-demographiques",
    DonneesProjectionDemographiqueViewSet,
    basename="projections-demographiques"
)
router.register(
    r"migrations-internes",
    DonneesMigrationInterneViewSet,
    basename="migrations-internes"
)

urlpatterns = [
    path("", include(router.urls)),
]