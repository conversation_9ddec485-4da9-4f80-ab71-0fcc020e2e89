#!/usr/bin/env python3
"""
Script d'installation et de configuration de Jupyter pour Kaydan Analytics Hub
Installe toutes les dépendances nécessaires pour les notebooks d'analyse
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Exécute une commande et affiche le résultat"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"   ✅ {description} - Succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ {description} - Erreur: {e}")
        print(f"   📝 Output: {e.stdout}")
        print(f"   🚨 Error: {e.stderr}")
        return False

def install_packages():
    """Installe tous les packages Python nécessaires"""
    print("📦 INSTALLATION DES PACKAGES PYTHON")
    print("=" * 50)
    
    packages = [
        "jupyter",
        "jupyterlab", 
        "pandas",
        "numpy",
        "matplotlib",
        "seaborn",
        "plotly",
        "openpyxl",
        "xlsxwriter"
    ]
    
    for package in packages:
        success = run_command(f"pip install {package}", f"Installation de {package}")
        if not success:
            print(f"⚠️ Échec de l'installation de {package}")
    
    print("\n✅ Installation des packages terminée !")

def setup_jupyter_config():
    """Configure Jupyter avec les bonnes extensions"""
    print("\n🔧 CONFIGURATION DE JUPYTER")
    print("=" * 50)
    
    # Installation des extensions Jupyter
    extensions = [
        "jupyter contrib nbextension install --user",
        "jupyter nbextension enable --py widgetsnbextension",
    ]
    
    for ext in extensions:
        run_command(ext, f"Configuration: {ext}")

def create_jupyter_config():
    """Crée un fichier de configuration Jupyter personnalisé"""
    print("\n📝 CRÉATION DE LA CONFIGURATION JUPYTER")
    print("=" * 50)
    
    config_content = '''# Configuration Jupyter pour Kaydan Analytics Hub
c = get_config()

# Configuration du serveur
c.NotebookApp.ip = '127.0.0.1'
c.NotebookApp.port = 8888
c.NotebookApp.open_browser = True
c.NotebookApp.notebook_dir = './notebooks'

# Configuration de l'affichage
c.InlineBackend.figure_format = 'retina'
c.InlineBackend.rc = {'figure.figsize': (12, 8)}

# Configuration de sécurité
c.NotebookApp.token = ''
c.NotebookApp.password = ''
c.NotebookApp.disable_check_xsrf = False

# Extensions
c.NotebookApp.nbserver_extensions = {
    'jupyter_nbextensions_configurator': True,
}
'''
    
    # Créer le dossier de configuration s'il n'existe pas
    config_dir = os.path.expanduser("~/.jupyter")
    os.makedirs(config_dir, exist_ok=True)
    
    config_file = os.path.join(config_dir, "jupyter_notebook_config.py")
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        print(f"   ✅ Configuration créée: {config_file}")
    except Exception as e:
        print(f"   ❌ Erreur lors de la création de la configuration: {e}")

def create_startup_script():
    """Crée un script de démarrage pour Jupyter"""
    print("\n🚀 CRÉATION DU SCRIPT DE DÉMARRAGE")
    print("=" * 50)
    
    startup_script = '''#!/bin/bash
# Script de démarrage Jupyter pour Kaydan Analytics Hub

echo "🚀 Démarrage de Jupyter Notebook pour Kaydan Analytics Hub"
echo "=" * 60

# Vérification de l'environnement virtuel
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ Environnement virtuel actif: $VIRTUAL_ENV"
else
    echo "⚠️ Aucun environnement virtuel détecté"
    echo "💡 Conseil: Activez votre environnement virtuel avec 'source venv/bin/activate'"
fi

# Vérification des dossiers
if [ ! -d "notebooks" ]; then
    echo "❌ Dossier 'notebooks' non trouvé"
    echo "💡 Assurez-vous d'être dans le répertoire racine du projet"
    exit 1
fi

if [ ! -d "exports" ]; then
    echo "📁 Création du dossier 'exports'"
    mkdir -p exports
fi

echo "📚 Lancement de Jupyter Notebook..."
echo "🌐 URL: http://127.0.0.1:8888"
echo "📋 Commencez par: notebooks/00_INDEX_NOTEBOOKS.ipynb"
echo ""
echo "🛑 Pour arrêter: Ctrl+C"
echo ""

# Lancement de Jupyter
cd notebooks && jupyter notebook --ip=127.0.0.1 --port=8888 --no-browser
'''
    
    try:
        with open("start_jupyter.sh", 'w', encoding='utf-8') as f:
            f.write(startup_script)
        
        # Rendre le script exécutable
        os.chmod("start_jupyter.sh", 0o755)
        print("   ✅ Script de démarrage créé: start_jupyter.sh")
    except Exception as e:
        print(f"   ❌ Erreur lors de la création du script: {e}")

def create_requirements_jupyter():
    """Crée un fichier requirements spécifique pour Jupyter"""
    print("\n📋 CRÉATION DU FICHIER REQUIREMENTS JUPYTER")
    print("=" * 50)
    
    requirements_content = '''# Requirements pour les notebooks Jupyter - Kaydan Analytics Hub
# Installation: pip install -r requirements_jupyter.txt

# Jupyter
jupyter==1.0.0
jupyterlab==4.0.9
notebook==7.0.6

# Data Science
pandas==2.1.4
numpy==1.25.2
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Export/Import
openpyxl==3.1.2
xlsxwriter==3.1.9

# Extensions Jupyter
jupyter_contrib_nbextensions==0.7.0
widgetsnbextension==4.0.9

# Utilitaires
ipywidgets==8.1.1
tqdm==4.66.1
'''
    
    try:
        with open("requirements_jupyter.txt", 'w', encoding='utf-8') as f:
            f.write(requirements_content)
        print("   ✅ Fichier requirements_jupyter.txt créé")
    except Exception as e:
        print(f"   ❌ Erreur lors de la création du fichier: {e}")

def main():
    """Fonction principale"""
    print("🎯 SETUP JUPYTER POUR KAYDAN ANALYTICS HUB")
    print("=" * 60)
    print("Ce script va installer et configurer Jupyter Notebook")
    print("pour l'analyse des données de tous les modèles Django.")
    print("")
    
    # Vérification de Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8+ requis")
        print(f"   Version actuelle: {python_version.major}.{python_version.minor}")
        sys.exit(1)
    
    print(f"✅ Python {python_version.major}.{python_version.minor} détecté")
    
    # Installation et configuration
    install_packages()
    setup_jupyter_config()
    create_jupyter_config()
    create_startup_script()
    create_requirements_jupyter()
    
    print("\n🎉 SETUP TERMINÉ AVEC SUCCÈS !")
    print("=" * 60)
    print("📋 Prochaines étapes:")
    print("1. 🚀 Lancez Jupyter: ./start_jupyter.sh")
    print("2. 🌐 Ouvrez: http://127.0.0.1:8888")
    print("3. 📚 Commencez par: 00_INDEX_NOTEBOOKS.ipynb")
    print("")
    print("💡 Conseils:")
    print("- Activez votre environnement virtuel avant de lancer")
    print("- Assurez-vous que Django fonctionne")
    print("- Consultez notebooks/README.md pour plus d'infos")

if __name__ == "__main__":
    main()
